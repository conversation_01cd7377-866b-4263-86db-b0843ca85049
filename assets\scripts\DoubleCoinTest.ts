import { _decorator, Component, Node } from 'cc';
import { ItemManager, ItemType } from './ItemManager';
import { GameData } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 双倍金币卡功能测试脚本
 * 用于验证双倍金币卡的各项功能是否正常工作
 */
@ccclass('DoubleCoinTest')
export class DoubleCoinTest extends Component {

    start() {
        // 延迟执行测试，确保所有系统已初始化
        this.scheduleOnce(() => {
            this.runTests();
        }, 1.0);
    }

    private runTests() {
        console.log("=== 开始双倍金币卡功能测试 ===");

        // 测试1: 基础功能测试
        this.testBasicFunctions();

        // 测试2: 购买功能测试
        this.testPurchaseFunction();

        // 测试3: 使用功能测试
        this.testUseFunction();

        // 测试4: 金币倍数测试
        this.testCoinMultiplier();

        console.log("=== 双倍金币卡功能测试完成 ===");
    }

    private testBasicFunctions() {
        console.log("\n--- 测试1: 基础功能测试 ---");

        // 测试道具配置
        const price = ItemManager.getItemPrice(ItemType.DOUBLE_COIN);
        const name = ItemManager.getItemName(ItemType.DOUBLE_COIN);
        const description = ItemManager.getItemDescription(ItemType.DOUBLE_COIN);

        console.log(`道具名称: ${name}`);
        console.log(`道具价格: ${price}`);
        console.log(`道具描述: ${description}`);

        // 验证初始状态
        const initialCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const initialActive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        const initialMultiplier = ItemManager.getCoinMultiplier();

        console.log(`初始数量: ${initialCount}`);
        console.log(`初始激活状态: ${initialActive}`);
        console.log(`初始倍数: ${initialMultiplier}`);

        if (price === 50 && name === "双倍金币卡" && initialMultiplier === 1) {
            console.log("✅ 基础功能测试通过");
        } else {
            console.error("❌ 基础功能测试失败");
        }
    }

    private testPurchaseFunction() {
        console.log("\n--- 测试2: 购买功能测试 ---");

        // 设置足够的金币进行测试
        const testCoins = 1000;
        localStorage.setItem("TotalCoins", testCoins.toString());
        console.log(`设置测试金币: ${testCoins}`);

        const initialCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const initialTotalCoins = GameData.getTotalCoins();

        // 尝试购买
        const purchaseSuccess = ItemManager.purchaseItem(ItemType.DOUBLE_COIN);

        const finalCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const finalTotalCoins = GameData.getTotalCoins();

        console.log(`购买前: 数量=${initialCount}, 金币=${initialTotalCoins}`);
        console.log(`购买结果: ${purchaseSuccess}`);
        console.log(`购买后: 数量=${finalCount}, 金币=${finalTotalCoins}`);

        if (purchaseSuccess && finalCount === initialCount + 1 && finalTotalCoins === initialTotalCoins - 50) {
            console.log("✅ 购买功能测试通过");
        } else {
            console.error("❌ 购买功能测试失败");
        }
    }

    private testUseFunction() {
        console.log("\n--- 测试3: 使用功能测试 ---");

        const initialCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const initialActive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        const initialMultiplier = ItemManager.getCoinMultiplier();

        console.log(`使用前: 数量=${initialCount}, 激活=${initialActive}, 倍数=${initialMultiplier}`);

        // 尝试使用道具
        const useSuccess = ItemManager.useItem(ItemType.DOUBLE_COIN);

        const finalCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const finalActive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        const finalMultiplier = ItemManager.getCoinMultiplier();

        console.log(`使用结果: ${useSuccess}`);
        console.log(`使用后: 数量=${finalCount}, 激活=${finalActive}, 倍数=${finalMultiplier}`);

        if (useSuccess && finalCount === initialCount - 1 && finalActive && finalMultiplier === 2) {
            console.log("✅ 使用功能测试通过");
        } else {
            console.error("❌ 使用功能测试失败");
        }

        // 测试禁用功能
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);
        const deactivatedActive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);
        const deactivatedMultiplier = ItemManager.getCoinMultiplier();

        console.log(`禁用后: 激活=${deactivatedActive}, 倍数=${deactivatedMultiplier}`);

        if (!deactivatedActive && deactivatedMultiplier === 1) {
            console.log("✅ 禁用功能测试通过");
        } else {
            console.error("❌ 禁用功能测试失败");
        }
    }

    private testCoinMultiplier() {
        console.log("\n--- 测试4: 金币倍数测试 ---");

        // 重置游戏数据
        GameData.resetSessionCoins();

        // 激活双倍金币卡
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);

        // 模拟收集金币
        const testCoinAmount = 10;
        for (let i = 0; i < testCoinAmount; i++) {
            GameData.addCoin(1);
        }

        const sessionCoins = GameData.getSessionCoins();
        const finalSessionCoins = GameData.getFinalSessionCoins();
        const multiplier = GameData.getCurrentCoinMultiplier();

        console.log(`原始本局金币: ${sessionCoins}`);
        console.log(`最终本局金币: ${finalSessionCoins}`);
        console.log(`当前倍数: ${multiplier}`);

        if (sessionCoins === testCoinAmount && finalSessionCoins === testCoinAmount * 2 && multiplier === 2) {
            console.log("✅ 金币倍数测试通过");
        } else {
            console.error("❌ 金币倍数测试失败");
        }

        // 测试应用效果到总金币
        const beforeTotal = GameData.getTotalCoins();
        GameData.applyItemEffectsToTotalCoins();
        const afterTotal = GameData.getTotalCoins();

        console.log(`应用效果前总金币: ${beforeTotal}`);
        console.log(`应用效果后总金币: ${afterTotal}`);

        if (afterTotal === beforeTotal + testCoinAmount) {
            console.log("✅ 总金币应用效果测试通过");
        } else {
            console.error("❌ 总金币应用效果测试失败");
        }

        // 清理测试效果
        ItemManager.clearGameEffects();
        console.log("已清理测试效果");
    }

    // 静态方法，可以从控制台手动调用
    public static runManualTest() {
        console.log("=== 手动测试双倍金币卡功能 ===");

        const testInstance = new DoubleCoinTest();
        testInstance.runTests();
    }

    // 重置测试环境
    public static resetTestEnvironment() {
        console.log("重置测试环境...");

        // 清除道具数据
        ItemManager.setItemCount(ItemType.DOUBLE_COIN, 0);
        ItemManager.deactivateItemEffect(ItemType.DOUBLE_COIN);

        // 重置金币数据
        GameData.resetSessionCoins();
        localStorage.setItem("TotalCoins", "100"); // 设置默认金币

        console.log("测试环境已重置");
    }

    // 测试持续激活逻辑
    public static testPersistentActivation() {
        console.log("=== 测试持续激活逻辑 ===");

        // 设置测试环境：有2个双倍金币卡
        ItemManager.setItemCount(ItemType.DOUBLE_COIN, 2);
        ItemManager.activateItemEffect(ItemType.DOUBLE_COIN);

        console.log("初始状态:");
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        console.log(`- 倍数: ${ItemManager.getCoinMultiplier()}`);

        // 模拟第一局游戏结束
        console.log("\n第一局游戏结束:");
        ItemManager.consumeActiveItems();
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);

        // 模拟第二局游戏开始
        console.log("\n第二局游戏开始:");
        ItemManager.resetAllEffects();
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);
        console.log(`- 倍数: ${ItemManager.getCoinMultiplier()}`);

        // 模拟第二局游戏结束
        console.log("\n第二局游戏结束:");
        ItemManager.consumeActiveItems();
        console.log(`- 数量: ${ItemManager.getItemCount(ItemType.DOUBLE_COIN)}`);
        console.log(`- 激活: ${ItemManager.isItemActive(ItemType.DOUBLE_COIN)}`);

        // 验证结果
        const finalCount = ItemManager.getItemCount(ItemType.DOUBLE_COIN);
        const finalActive = ItemManager.isItemActive(ItemType.DOUBLE_COIN);

        if (finalCount === 0 && !finalActive) {
            console.log("✅ 持续激活逻辑测试通过");
        } else {
            console.error("❌ 持续激活逻辑测试失败");
        }

        console.log("=== 持续激活逻辑测试完成 ===");
    }
}
