[{"__type__": "cc.AnimationClip", "_name": "Bird_albatross_home", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 6, "speed": 1, "wrapMode": 2, "enableTrsBlending": false, "_duration": 0.8333333333333334, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 6}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 16}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channel": {"__id__": 4}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "spriteFrame"]}, {"__type__": "cc.animation.ComponentPath", "component": "cc.Sprite"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.ObjectCurve", "_times": [0.16666666666666666, 0.3333333333333333, 0.5, 0.6666666666666666], "_values": [{"__uuid__": "65ce5e57-3f94-457a-81ba-45ae77f763dc@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "858482dd-7198-4c5c-bcd4-7c8bf05ff1ed@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "6808320f-4c5b-4150-ad05-ff6b94aea123@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d513a736-2df4-448c-80dc-5704a5bdaa19@f9941", "__expectedType__": "cc.SpriteFrame"}]}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 7}, "proxy": null}, "_channels": [{"__id__": 8}, {"__id__": 10}, {"__id__": 12}, {"__id__": 14}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": ["position"]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 13}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]