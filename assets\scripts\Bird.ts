import { _decorator, Animation, animation, AudioClip, Collider2D, Component, Contact2DType, Input, input, IPhysics2DContact, Node, RigidBody2D, SkelAnimDataHub, Vec2, Vec3 } from 'cc';
import { Tags } from './Tags';
import { GameManager } from './GameManager';
import { AudioMgr } from './AudioMgr';
import { GameDifficulty } from './GameDifficulty';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
const { ccclass, property } = _decorator;

@ccclass('Bird')
export class Bird extends Component {

    private rgd2D:RigidBody2D = null;

    @property
    rotateSpeed:number = 30;

    // 不同难度的跳跃力度
    private readonly JUMP_FORCE_EASY: number = 12;
    private readonly JUMP_FORCE_NORMAL: number = 15;
    private readonly JUMP_FORCE_HARD: number = 18;
    
    // 雪花效果的跳跃力度
    private readonly JUMP_FORCE_SNOW_EFFECT: number = 10;
    
    // 雪花效果持续时间（秒）
    private readonly SNOW_EFFECT_DURATION: number = 2.0;
    
    // 雪花效果计时器
    private snowEffectTimer: number = 0;
    
    // 是否受到雪花影响
    private isAffectedBySnow: boolean = false;

    @property(AudioClip)
    clickAudio:AudioClip = null;

    private _canControl:boolean = false;

    onLoad () {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);

        // 注册单个碰撞体的回调函数
        let collider = this.getComponent(Collider2D);
        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
            collider.on(Contact2DType.END_CONTACT, this.onEndContact, this);
        }

        this.rgd2D = this.getComponent(RigidBody2D);

        // 根据难度设置重力
        this.applyDifficultySettings();
    }

    // 根据难度设置重力
    private applyDifficultySettings() {
        if (!this.rgd2D) return;

        // 获取当前难度的重力值
        const gravityValue = GameManager.inst().getCurrentGravityValue();

        // 直接设置重力值
        this.rgd2D.gravityScale = gravityValue;

        console.log(`小鸟重力设置为: ${this.rgd2D.gravityScale}`);
    }

    onDestroy () {
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);

        let collider = this.getComponent(Collider2D);
        if (collider) {
            collider.off(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
            collider.off(Contact2DType.END_CONTACT, this.onEndContact, this);
        }
    }

    onTouchStart(){
        if(this._canControl==false)return;

        // 获取跳跃力度
        let jumpForce = this.getCurrentJumpForce();

        // 应用跳跃力度
        this.rgd2D.linearVelocity = new Vec2(0, jumpForce);

        this.node.angle = 30;
        AudioMgr.inst.playOneShot(this.clickAudio,7.0);
    }
    
    /**
     * 获取当前跳跃力度
     * 根据难度和是否受雪花影响决定
     */
    private getCurrentJumpForce(): number {
        // 如果受到雪花影响，返回降低的跳跃力度
        if (this.isAffectedBySnow) {
            return this.JUMP_FORCE_SNOW_EFFECT;
        }
        
        // 否则根据当前难度返回正常跳跃力度
        const currentDifficulty = GameDifficulty.getDifficulty();
        
        switch(currentDifficulty) {
            case GameDifficulty.DIFFICULTY_NORMAL:
                return this.JUMP_FORCE_NORMAL;
            case GameDifficulty.DIFFICULTY_HARD:
                return this.JUMP_FORCE_HARD;
            default:
                return this.JUMP_FORCE_EASY;
        }
    }

    protected update(dt: number): void {
        if(this._canControl==false)return;
        
        // 更新小鸟旋转
        this.node.angle -= this.rotateSpeed*dt;
        if(this.node.angle<-60){
            this.node.angle = -60;
        }
        
        // 更新雪花效果计时器
        this.updateSnowEffect(dt);
    }
    
    /**
     * 更新雪花效果计时器
     */
    private updateSnowEffect(dt: number) {
        // 如果正在受到雪花影响
        if (this.isAffectedBySnow) {
            // 减少计时器
            this.snowEffectTimer -= dt;
            
            // 如果计时器结束，移除效果
            if (this.snowEffectTimer <= 0) {
                this.removeSnowEffect();
            }
        }
    }
    
    /**
     * 应用雪花效果
     * 当小鸟碰到雪花时调用
     */
    public applySnowEffect() {
        console.log("小鸟受到雪花影响，跳跃力度降低！");
        
        // 标记为受到雪花影响
        this.isAffectedBySnow = true;
        
        // 重置计时器（无论是新效果还是刷新已有效果）
        this.snowEffectTimer = this.SNOW_EFFECT_DURATION;
    }
    
    /**
     * 移除雪花效果
     */
    private removeSnowEffect() {
        console.log("雪花效果结束，跳跃力度恢复正常");
        
        // 移除标记
        this.isAffectedBySnow = false;
        this.snowEffectTimer = 0;
    }

    public enableControl(){
        this.getComponent(Animation).enabled=true;
        this.rgd2D.enabled=true;
        this._canControl=true;

        // 确保应用正确的难度设置
        this.applyDifficultySettings();
    }
    public disableControl(){
        this.getComponent(Animation).enabled=false;
        this.rgd2D.enabled=false;
        this._canControl = false;
    }

    public disableControlNotRGD(){
        this.getComponent(Animation).enabled=false;
        this._canControl = false;
    }

    onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null){
        console.log("小鸟碰撞: 标签=", otherCollider.tag, "名称=", otherCollider.node.name);

        // 检测是否与地面或管道碰撞
        if(otherCollider.tag===Tags.LAND || otherCollider.tag===Tags.PIPE){
            GameManager.inst().transitionToGameOverState();
        }

        // 金币碰撞现在由金币自己处理，不再需要在这里处理
    }

    onEndContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact | null){
        // 检测是否通过管道中间
        if(otherCollider.tag===Tags.PIPE_MIDDLE){
            GameManager.inst().addScore();
        }
    }

}


