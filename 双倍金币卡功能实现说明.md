# 双倍金币卡功能实现说明

## 功能概述

已成功实现双倍金币卡功能，包括：
- 商店中购买双倍金币卡（价格50金币）
- 使用/禁用双倍金币卡
- 游戏中金币翻倍效果
- 游戏结束界面显示倍数和最终金币数

## 新增文件

### 1. `assets/scripts/ItemManager.ts`
- 道具管理器，负责所有道具的购买、使用、状态管理
- 支持双倍金币卡和未来扩展的其他道具

### 2. `assets/scripts/UI/DoubleCoinCard.ts`
- 双倍金币卡UI控制器
- 处理购买、使用、禁用按钮的逻辑

## 修改的文件

### 1. `assets/scripts/GameData.ts`
- 新增 `getFinalSessionCoins()` - 获取应用道具效果后的最终金币数
- 新增 `getCurrentCoinMultiplier()` - 获取当前金币倍数
- 新增 `applyItemEffectsToTotalCoins()` - 应用道具效果到总金币

### 2. `assets/scripts/UI/GameOverUI.ts`
- 新增 `magnificationLabel` 属性 - 显示倍数标签
- 修改显示逻辑，支持倍数显示和条件性显示SessionCoins
- 游戏结束时清理道具效果

### 3. `assets/scripts/GameManager.ts`
- 游戏开始时重置道具效果

## Cocos Creator 手动配置清单

### 1. Shop场景配置

#### 在 `Shop.scene` 中的 `Double_coin` 节点下：

**需要添加的组件：**
1. 给 `Double_coin` 节点添加 `DoubleCoinCard` 组件

**需要配置的属性：**
- `Purchase Button`: 拖拽购买按钮节点
- `Use Button`: 拖拽使用按钮节点
- `Disable Button`: 拖拽禁用按钮节点
- `Price Label`: 拖拽价格标签节点
- `Number Label`: 拖拽数量标签节点
- `Insufficient Sprite`: 拖拽数量不足提示节点

**按钮配置：**
- 确保使用按钮和禁用按钮位置完全一致
- 初始状态：使用按钮显示，禁用按钮隐藏
- 价格标签设置为 "50"
- 数量标签设置为 "0"
- 数量不足提示节点初始状态：隐藏

### 2. Game场景配置

#### 在 `Game.scene` 中：

**需要添加的组件：**
1. 在场景中任意节点（建议GameManager节点）添加 `ItemManager` 组件

#### 在 `GameOverUI` 节点下：

**需要添加的节点和组件：**
1. 添加 `Magnification` 标签节点
2. 在 `GameOverUI` 组件中配置：
   - `Magnification Label`: 拖拽新添加的倍数标签节点

**标签配置：**
- `Magnification` 标签初始状态：隐藏
- `SessionCoins` 标签：保持现有配置，脚本会控制显示/隐藏

### 3. 详细配置步骤

#### Step 1: 配置Shop场景的Double_coin节点
```
1. 选择 Shop.scene
2. 找到 Double_coin 节点
3. 添加组件：DoubleCoinCard
4. 配置组件属性：
   - Purchase Button: 拖拽 PurchaseButton 节点
   - Use Button: 拖拽 UseButton 节点
   - Disable Button: 拖拽 DisableButton 节点
   - Price Label: 拖拽 PriceLabel 节点
   - Number Label: 拖拽 NumberLabel 节点
   - Insufficient Sprite: 拖拽数量不足提示节点
```

#### Step 2: 配置Game场景的ItemManager
```
1. 选择 Game.scene
2. 选择 GameManager 节点（或其他合适节点）
3. 添加组件：ItemManager
```

#### Step 3: 配置GameOverUI的倍数显示
```
1. 在 Game.scene 中找到 GameOverUI 节点
2. 在 ScorePanel 下添加新的 Label 节点，命名为 "Magnification"
3. 设置 Magnification 标签的样式（字体、颜色、大小等）
4. 在 GameOverUI 组件中：
   - 将 Magnification 节点拖拽到 Magnification Label 属性
5. 设置 Magnification 标签初始状态为隐藏（Active = false）
```

## 功能逻辑说明

### 购买逻辑
1. 点击购买按钮 → 检查金币是否足够
2. 扣除50金币 → 数量标签+1
3. 更新按钮状态

### 使用逻辑
1. 点击使用按钮 → 检查是否有道具
2. 如果有道具：激活双倍效果，隐藏使用按钮，显示禁用按钮
3. 如果没有道具：显示数量不足提示，2秒后自动隐藏

### 禁用逻辑
1. 点击禁用按钮 → 取消双倍效果
2. 隐藏禁用按钮 → 显示使用按钮

### 游戏中效果
1. 游戏开始时检查是否有激活的双倍金币卡且数量>0
2. 如果有，本局所有金币收益×2
3. 游戏结束时显示倍数和最终金币数
4. 消耗1个激活的道具
5. 如果消耗后仍有剩余数量，保持激活状态；否则取消激活
6. 重新开始游戏时，自动检查激活状态并应用效果

### 显示逻辑
- SessionCoins 标签：始终显示本局金币数（有倍数时显示翻倍后的数值）
- 倍数 > 1：显示 Magnification 标签
- 倍数 = 1：隐藏 Magnification 标签
- Magnification 格式：`X2!`、`X3!` 等

## 测试步骤

1. **购买测试**：在商店中购买双倍金币卡，检查金币扣除和数量增加
2. **使用测试**：点击使用按钮，检查按钮状态切换
3. **游戏测试**：使用道具后进入游戏，收集金币，检查游戏结束界面显示
4. **倍数测试**：验证金币数是否正确翻倍
5. **清理测试**：游戏结束后检查道具效果是否正确清理

## 注意事项

1. 确保所有按钮都正确连接了点击事件
2. 使用按钮和禁用按钮的位置必须完全一致
3. 价格标签和数量标签的初始值要正确设置
4. ItemManager组件必须添加到Game场景中
5. 所有标签的显示/隐藏逻辑由脚本自动控制

配置完成后，双倍金币卡功能将在所有游戏模式中正常工作！

## 🔧 最新修复的逻辑流程

### 完整使用流程：
1. **购买** → 扣除50金币 → 数量+1
2. **使用** → 只激活效果，不减少数量
3. **游戏中** → 金币收益×2（如果激活）
4. **游戏结束** → 显示倍数和最终金币 → 消耗1个道具 → 有剩余则保持激活
5. **重新开始** → 自动检查激活状态 → 有效则继续应用效果
6. **持续使用** → 只要有数量且未手动禁用，每局都自动生效

### 关键特性：
- ✅ 使用按钮始终显示（数量不足时显示提示）
- ✅ 道具在游戏结束时才消耗，不是点击使用时消耗
- ✅ 有剩余数量时自动保持激活状态
- ✅ SessionCoins标签始终显示
- ✅ 支持连续多局自动生效
