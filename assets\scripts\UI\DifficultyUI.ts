import { _decorator, Component, director, Node } from 'cc';
import { GameDifficulty } from '../GameDifficulty';
import { AudioMgr } from '../AudioMgr';
import { ChallengeMode, ChallengeModeType } from '../ChallengeMode';
const { ccclass, property } = _decorator;

@ccclass('DifficultyUI')
export class DifficultyUI extends Component {
    @property(Node)
    mainPanel: Node = null;

    start() {
        // 初始化
    }

    // 点击轻松按钮
    onEasyBtnClick() {
        // 设置难度为轻松
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_EASY);
        
        // 清除挑战模式状态，确保不会显示雾效果
        ChallengeMode.clearMode();
        console.log("普通模式-轻松难度：清除挑战模式状态");

        // 停止主菜单背景音乐，避免与游戏场景的音乐重叠
        AudioMgr.inst.stop();

        // 加载游戏场景
        director.loadScene('Game');
    }

    // 点击标准按钮
    onNormalBtnClick() {
        // 设置难度为标准
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_NORMAL);
        
        // 清除挑战模式状态，确保不会显示雾效果
        ChallengeMode.clearMode();
        console.log("普通模式-标准难度：清除挑战模式状态");

        // 停止主菜单背景音乐，避免与游戏场景的音乐重叠
        AudioMgr.inst.stop();

        // 加载游戏场景
        director.loadScene('Game');
    }

    // 点击困难按钮
    onHardBtnClick() {
        // 设置难度为困难
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_HARD);
        
        // 清除挑战模式状态，确保不会显示雾效果
        ChallengeMode.clearMode();
        console.log("普通模式-困难难度：清除挑战模式状态");

        // 停止主菜单背景音乐，避免与游戏场景的音乐重叠
        AudioMgr.inst.stop();

        // 加载游戏场景
        director.loadScene('Game');
    }

    // 点击返回按钮
    onBackBtnClick() {
        // 隐藏难度选择面板，显示主面板
        this.node.active = false;
        this.mainPanel.active = true;
    }
}

