import { _decorator, Component, Label, Node } from 'cc';
import { GameData } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 金币显示组件，用于在Home场景中显示玩家拥有的总金币数量
 */
@ccclass('CoinDisplay')
export class CoinDisplay extends Component {
    @property(Label)
    coinCountLabel: Label = null;

    start() {
        // 确保Label组件已设置
        if (!this.coinCountLabel) {
            console.error("CoinDisplay错误: 未找到coinCountLabel组件!");
            return;
        }

        // 初始化显示
        this.updateDisplay();
    }

    /**
     * 更新金币显示
     */
    public updateDisplay() {
        if (this.coinCountLabel) {
            // 从GameData获取总金币数并显示
            const totalCoins = GameData.getTotalCoins();
            this.coinCountLabel.string = totalCoins.toString();
        }
    }

    update(_deltaTime: number) {
        // 每帧更新金币显示（如果需要实时更新）
        // 在实际应用中，可能只需要在特定事件触发时更新
        this.updateDisplay();
    }
}
