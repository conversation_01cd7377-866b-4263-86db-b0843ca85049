import { _decorator, Component, Node, Button } from 'cc';
import { BirdType, GameData } from '../GameData';
const { ccclass, property } = _decorator;

@ccclass('CharacterPanel')
export class CharacterPanel extends Component {
    // 所有角色节点数组
    @property([Node])
    characterNodes: Node[] = [];

    // 左右切换按钮
    @property(Node)
    leftButton: Node = null;

    @property(Node)
    rightButton: Node = null;

    // 当前显示的角色索引
    private currentCharacterIndex: number = 0;

    start() {
        // 从GameData中获取上次选择的小鸟类型
        const savedBirdType = GameData.getSelectedBirdType();
        // 将保存的小鸟类型作为当前索引
        this.currentCharacterIndex = savedBirdType;
        console.log("CharacterPanel: 从存储中加载角色类型：", savedBirdType);
        
        // 初始化：显示保存的角色，隐藏其他角色
        this.showCharacterAtIndex(this.currentCharacterIndex);

        // 检查按钮是否存在并有效
        if (this.leftButton && this.leftButton.isValid) {
            // 为左按钮添加点击事件
            const leftBtnComp = this.leftButton.getComponent(Button);
            if (leftBtnComp) {
                leftBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.leftButton.on(Button.EventType.CLICK, this.onLeftButtonClick, this);
            }
        }

        if (this.rightButton && this.rightButton.isValid) {
            // 为右按钮添加点击事件
            const rightBtnComp = this.rightButton.getComponent(Button);
            if (rightBtnComp) {
                rightBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.rightButton.on(Button.EventType.CLICK, this.onRightButtonClick, this);
            }
        }

        console.log("CharacterPanel初始化完成，当前显示角色索引：", this.currentCharacterIndex);
    }

    onDestroy() {
        // 移除事件监听，防止内存泄漏
        if (this.leftButton && this.leftButton.isValid) {
            this.leftButton.off(Button.EventType.CLICK, this.onLeftButtonClick, this);
        }

        if (this.rightButton && this.rightButton.isValid) {
            this.rightButton.off(Button.EventType.CLICK, this.onRightButtonClick, this);
        }
    }

    // 显示指定索引的角色，隐藏其他角色
    showCharacterAtIndex(index: number) {
        if (this.characterNodes.length === 0) {
            console.error("CharacterPanel: 没有配置角色节点！");
            return;
        }

        // 确保索引在有效范围内
        index = this.normalizeIndex(index);
        this.currentCharacterIndex = index;

        // 激活当前索引的角色，禁用其他角色
        for (let i = 0; i < this.characterNodes.length; i++) {
            this.characterNodes[i].active = (i === index);
        }

        // 保存当前选择的角色类型到GameData
        const selectedBirdType = index as BirdType;
        GameData.setSelectedBirdType(selectedBirdType);
        console.log("CharacterPanel: 切换到角色：", index, "，保存角色类型：", selectedBirdType);
    }

    // 处理索引循环（当索引超出范围时循环到另一端）
    normalizeIndex(index: number): number {
        const length = this.characterNodes.length;
        // 如果索引小于0，则循环到最后一个
        if (index < 0) {
            return length - 1;
        }
        // 如果索引超出范围，则循环到第一个
        if (index >= length) {
            return 0;
        }
        return index;
    }

    // 左按钮点击事件处理
    onLeftButtonClick() {
        console.log("CharacterPanel: 点击左按钮");
        // 切换到上一个角色
        this.showCharacterAtIndex(this.currentCharacterIndex - 1);
    }

    // 右按钮点击事件处理
    onRightButtonClick() {
        console.log("CharacterPanel: 点击右按钮");
        // 切换到下一个角色
        this.showCharacterAtIndex(this.currentCharacterIndex + 1);
    }
}
