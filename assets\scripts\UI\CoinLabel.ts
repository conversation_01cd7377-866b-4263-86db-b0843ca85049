import { _decorator, Component, Label } from 'cc';
import { GameData } from '../GameData';
const { ccclass, property } = _decorator;

/**
 * 金币标签组件，用于显示当前游戏中收集的金币数量
 */
@ccclass('CoinLabel')
export class CoinLabel extends Component {
    @property(Label)
    label: Label = null;

    start() {
        // 确保Label组件已设置
        if (!this.label) {
            this.label = this.getComponent(Label);
            if (!this.label) {
                console.error("CoinLabel错误: 未找到Label组件!");
                return;
            }
        }
        
        // 初始化显示
        this.updateDisplay();
    }

    /**
     * 更新金币显示
     */
    public updateDisplay() {
        if (this.label) {
            this.label.string = GameData.getSessionCoins().toString();
        }
    }
}
