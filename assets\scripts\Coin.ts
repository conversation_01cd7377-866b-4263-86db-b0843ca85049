import { _decorator, Component, Node, AudioClip } from 'cc';
import { GameManager } from './GameManager';
import { GameData } from './GameData';
import { AudioMgr } from './AudioMgr';
import { WindChallengeManager } from './WindChallengeManager';
const { ccclass, property } = _decorator;

@ccclass('Coin')
export class Coin extends Component {
    private moveSpeed: number = 200; // 默认移动速度
    private _isWindMode: boolean = false; // 是否为大风吹模式

    @property(AudioClip)
    collectSound: AudioClip = null; // 收集音效

    // 碰撞检测的距离阈值
    private readonly COLLISION_DISTANCE: number = 50;
    private _bird: Node = null;
    private _collected: boolean = false;

    start() {
        // 从GameManager获取基于当前难度的移动速度，确保与管道一致
        this.moveSpeed = GameManager.inst().getCurrentMoveSpeed();

        // 查找小鸟节点
        this._bird = GameManager.inst().bird.node;

        // 确保金币是激活的
        this.node.active = true;
        console.log("金币初始化完成: 位置=", this.node.position.x, this.node.position.y, "速度=", this.moveSpeed);
    }

    onDestroy() {
        // 清理资源
    }

    update(deltaTime: number) {
        // 在大风吹模式下使用动态速度
        let currentSpeed = this.moveSpeed;
        if (this._isWindMode && WindChallengeManager.isWindMode()) {
            const windManager = WindChallengeManager.getInstance();
            if (windManager) {
                currentSpeed = windManager.getCurrentSpeed();
            }
        }

        // 向左移动（与管道同步）
        const p = this.node.position;
        this.node.setPosition(p.x - currentSpeed * deltaTime, p.y);

        // 超出屏幕销毁
        if (p.x < -900) {
            this.node.destroy();
            return;
        }

        // 如果已经被收集，不再检测碰撞
        if (this._collected) return;

        // 检测与小鸟的距离
        if (this._bird && this._bird.isValid) {
            const birdPos = this._bird.getWorldPosition();
            const coinPos = this.node.getWorldPosition();

            // 计算距离
            const dx = birdPos.x - coinPos.x;
            const dy = birdPos.y - coinPos.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // 如果距离小于阈值，认为发生了碰撞
            if (distance < this.COLLISION_DISTANCE) {
                this.collectCoin();
            }
        }
    }

    // 收集金币的方法
    collectCoin() {
        // 防止重复收集
        if (this._collected) return;
        this._collected = true;

        console.log("金币被收集!");

        // 播放收集音效
        if (this.collectSound) {
            AudioMgr.inst.playOneShot(this.collectSound);
        }

        // 增加金币数并更新UI显示
        GameManager.inst().addCoin();

        // 安全销毁金币
        console.log("销毁金币...");

        // 延迟一帧再销毁
        this.scheduleOnce(() => {
            if (this.node && this.node.isValid) {
                try {
                    this.node.destroy();
                    console.log("金币已销毁");
                } catch (error) {
                    console.error("销毁金币时出错:", error);
                    // 如果销毁失败，至少隐藏它
                    this.node.active = false;
                }
            }
        }, 0);
    }

    /**
     * 设置大风吹模式
     */
    public setWindMode(isWindMode: boolean): void {
        this._isWindMode = isWindMode;
        console.log(`金币设置大风吹模式: ${isWindMode}`);
    }
}


