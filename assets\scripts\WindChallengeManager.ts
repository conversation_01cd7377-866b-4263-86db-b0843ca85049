import { _decorator, Component } from 'cc';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { GameManager } from './GameManager';
const { ccclass } = _decorator;

/**
 * 大风吹挑战模式管理器
 * 负责管理速度变化逻辑和距离控制的生成系统
 */
@ccclass('WindChallengeManager')
export class WindChallengeManager extends Component {

    // 单例实例
    private static _instance: WindChallengeManager = null;

    // 速度变化相关参数
    private readonly MIN_SPEED: number = 250;
    private readonly MAX_SPEED: number = 400;
    private readonly MIN_PIPES_TO_CHANGE: number = 3;
    private readonly MAX_PIPES_TO_CHANGE: number = 5;

    // 距离控制参数
    private readonly TOTAL_PIPE_DISTANCE: number = 600;    // 相邻管道的总距离

    // 当前状态
    private _currentSpeed: number = 150;  // 当前移动速度
    private _pipesPassedSinceLastChange: number = 0;  // 自上次速度变化后通过的管道数
    private _pipesToNextChange: number = 0;  // 到下次速度变化需要通过的管道数

    // 距离追踪
    private _totalDistanceTraveled: number = 0;  // 总移动距离
    private _nextPipeDistance: number = 300;     // 下次生成管道的距离（第一个管道在300距离处）
    private _nextCoinDistance: number = 600;     // 下次生成金币的距离（第一个金币在600距离处，即第一个管道后300距离）

    public static getInstance(): WindChallengeManager {
        return this._instance;
    }

    onLoad() {
        WindChallengeManager._instance = this;
    }

    start() {
        // 只在大风吹模式下初始化
        if (ChallengeMode.getMode() === ChallengeModeType.WIND) {
            this.initializeWindMode();
        }
    }

    /**
     * 初始化大风吹模式
     */
    private initializeWindMode(): void {
        console.log("初始化大风吹模式");

        // 设置初始速度为困难难度的基础速度
        this._currentSpeed = GameManager.inst().getCurrentMoveSpeed();

        // 随机设置第一次速度变化需要的管道数
        this._pipesToNextChange = this.getRandomPipeCount();

        console.log(`大风吹模式初始化完成 - 初始速度: ${this._currentSpeed}, 下次变化需要通过管道数: ${this._pipesToNextChange}`);
        console.log(`生成计划: 第一个管道距离=${this._nextPipeDistance}, 第一个金币距离=${this._nextCoinDistance}`);
    }

    /**
     * 获取随机的管道数量（3-5）
     */
    private getRandomPipeCount(): number {
        return Math.floor(Math.random() * (this.MAX_PIPES_TO_CHANGE - this.MIN_PIPES_TO_CHANGE + 1)) + this.MIN_PIPES_TO_CHANGE;
    }

    /**
     * 获取随机速度（150-250）
     */
    private getRandomSpeed(): number {
        return Math.floor(Math.random() * (this.MAX_SPEED - this.MIN_SPEED + 1)) + this.MIN_SPEED;
    }

    /**
     * 更新移动距离并检查是否需要生成管道或金币
     */
    public updateDistance(deltaTime: number): void {
        if (ChallengeMode.getMode() !== ChallengeModeType.WIND) {
            return;
        }

        // 累加移动距离
        this._totalDistanceTraveled += this._currentSpeed * deltaTime;

        // 检查是否需要生成管道
        this.checkPipeSpawn();

        // 检查是否需要生成金币
        this.checkCoinSpawn();
    }

    /**
     * 检查是否需要生成管道
     */
    private checkPipeSpawn(): void {
        if (this._totalDistanceTraveled >= this._nextPipeDistance) {
            // 通知生成管道
            const gameManager = GameManager.inst();
            if (gameManager) {
                // 获取当前激活的管道生成器
                const currentPipeSpawner = (gameManager as any).getCurrentPipeSpawner();
                if (currentPipeSpawner) {
                    // 调用管道生成器的强制生成方法
                    (currentPipeSpawner as any).forceSpawnPipe();
                }
            }

            // 更新距离记录
            this._nextPipeDistance = this._totalDistanceTraveled + this.TOTAL_PIPE_DISTANCE;

            // 增加通过的管道数
            this._pipesPassedSinceLastChange++;

            // 检查是否需要改变速度
            this.checkSpeedChange();

            console.log(`生成管道 - 距离: ${this._totalDistanceTraveled.toFixed(2)}, 下次管道距离: ${this._nextPipeDistance.toFixed(2)}`);
        }
    }

    /**
     * 检查是否需要生成金币
     */
    private checkCoinSpawn(): void {
        if (this._totalDistanceTraveled >= this._nextCoinDistance) {
            // 通知生成金币
            const gameManager = GameManager.inst();
            if (gameManager && gameManager.coinSpawner) {
                // 调用金币生成器的强制生成方法
                (gameManager.coinSpawner as any).forceSpawnCoin();
            }

            // 更新距离记录
            this._nextCoinDistance = this._totalDistanceTraveled + this.TOTAL_PIPE_DISTANCE;

            console.log(`生成金币 - 距离: ${this._totalDistanceTraveled.toFixed(2)}, 下次金币距离: ${this._nextCoinDistance.toFixed(2)}`);
        }
    }

    /**
     * 检查是否需要改变速度
     */
    private checkSpeedChange(): void {
        if (this._pipesPassedSinceLastChange >= this._pipesToNextChange) {
            // 改变速度
            const newSpeed = this.getRandomSpeed();
            this.changeSpeed(newSpeed);

            // 重置计数器
            this._pipesPassedSinceLastChange = 0;
            this._pipesToNextChange = this.getRandomPipeCount();

            console.log(`速度已改变 - 新速度: ${newSpeed}, 下次变化需要通过管道数: ${this._pipesToNextChange}`);
        }
    }

    /**
     * 改变移动速度
     */
    private changeSpeed(newSpeed: number): void {
        this._currentSpeed = newSpeed;

        // 通知所有移动组件更新速度
        const gameManager = GameManager.inst();
        if (gameManager) {
            // 更新背景移动速度
            if (gameManager.bgMoving) {
                (gameManager.bgMoving as any).updateSpeed(newSpeed);
            }
            if (gameManager.landMoving) {
                (gameManager.landMoving as any).updateSpeed(newSpeed);
            }
        }

        console.log(`大风吹模式 - 速度变化为: ${newSpeed}`);
    }

    /**
     * 获取当前速度
     */
    public getCurrentSpeed(): number {
        return this._currentSpeed;
    }

    /**
     * 重置状态（游戏重新开始时调用）
     */
    public reset(): void {
        this._pipesPassedSinceLastChange = 0;
        this._totalDistanceTraveled = 0;
        this._nextPipeDistance = 300;  // 第一个管道在300距离处
        this._nextCoinDistance = 600;  // 第一个金币在600距离处（第一个管道后300距离）

        // 重新设置初始速度和变化间隔
        if (ChallengeMode.getMode() === ChallengeModeType.WIND) {
            this._currentSpeed = GameManager.inst().getCurrentMoveSpeed();
            this._pipesToNextChange = this.getRandomPipeCount();
        }

        console.log("大风吹模式状态已重置");
    }

    /**
     * 检查是否为大风吹模式
     */
    public static isWindMode(): boolean {
        return ChallengeMode.getMode() === ChallengeModeType.WIND;
    }
}
