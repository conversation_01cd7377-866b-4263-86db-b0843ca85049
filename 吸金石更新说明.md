# 吸金石功能更新说明

## 更新内容

根据你的要求，已将吸金石修改为永久道具，主要变更如下：

### 1. 价格调整
- **原价格**: 100金币
- **新价格**: 2500金币
- **目的**: 测试金币不足提示功能

### 2. 道具性质变更
- **原设计**: 消耗型道具，游戏结束时消耗
- **新设计**: 永久道具，购买一次即可永久使用

### 3. 使用逻辑变更
- **购买后**: 自动激活吸金石效果
- **商店中**: 可随时使用/禁用按钮切换效果
- **游戏中**: 效果一直保持，直到在商店中手动禁用

## 具体变更

### ItemManager.ts 变更
1. **价格配置**: 100 → 2500金币
2. **描述更新**: "购买后永久生效，自动吸取半径400范围内的金币"
3. **消耗逻辑**: 移除游戏结束时的消耗代码
4. **永久性**: 吸金石不再被`consumeActiveItems()`消耗

### MagnetCard.ts 变更
1. **购买逻辑**: 购买成功后自动激活效果
2. **按钮显示**:
   - 未购买：显示购买按钮
   - 已购买且激活：显示禁用按钮
   - 已购买且未激活：显示使用按钮
3. **UI反馈**: 购买后隐藏购买按钮，显示禁用按钮

### 测试文件更新
- 更新所有测试用例以适应永久道具逻辑
- 修改价格相关测试（100 → 2500）
- 更新消耗测试为永久性测试

## 功能流程

### 购买流程
1. 玩家点击购买按钮
2. 检查金币是否≥2500
3. **金币足够**：
   - 扣除2500金币
   - 获得永久吸金石（数量设为1）
   - 自动激活效果（收集距离→400）
   - 隐藏购买按钮，显示禁用按钮
4. **金币不足**：
   - 显示`card_lack`提示2秒

### 使用/禁用流程
1. **点击使用按钮**：
   - 激活吸金石效果
   - 收集距离→400
   - 显示禁用按钮
2. **点击禁用按钮**：
   - 取消吸金石效果
   - 收集距离→50
   - 显示使用按钮

### 游戏中效果
- 激活状态下，金币收集距离为400
- 效果在所有游戏模式中生效
- 游戏结束后效果保持，不会被消耗
- 下次进入游戏时效果继续生效

## 测试金币不足功能

由于价格设置为2500金币，在大多数情况下玩家金币不足，可以测试：

1. **进入商店**: 道具面板 → 吸金石
2. **点击购买**: 如果金币<2500，会显示金币不足提示
3. **提示显示**: `card_lack` sprite显示2秒后自动隐藏

## 配置要求

在Cocos Creator中需要确保：

1. **MagnetCard组件配置**：
   - 5个属性正确引用（按钮、价格标签、提示节点）
   - 不需要NumberLabel（吸金石是唯一道具）
   - `card_lack`节点配置为金币不足提示

2. **价格显示**：
   - `PriceLabel`会自动显示"2500"

3. **按钮状态**：
   - 购买按钮：仅未购买时显示
   - 使用按钮：已购买但未激活时显示
   - 禁用按钮：已购买且已激活时显示

## 注意事项

1. **永久性**: 吸金石购买后永久拥有，不会消失
2. **状态保持**: 激活状态会跨场景和游戏会话保持
3. **价格测试**: 2500金币的高价格便于测试金币不足提示
4. **一次购买**: 每个玩家只需购买一次即可永久使用

## 完成状态

✅ **代码实现完成**
⚙️ **需要Cocos Creator配置**
🧪 **测试工具已更新**

按照配置说明完成Cocos Creator中的设置后，即可测试金币不足提示和永久道具功能。
