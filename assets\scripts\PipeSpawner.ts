import { _decorator, Component, instantiate, math, Node, Prefab } from 'cc';
import { Pipe } from './Pipe';
import { MovingPipe } from './MovingPipe';
import { GameDifficulty } from './GameDifficulty';
import { GameManager } from './GameManager';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
import { WindChallengeManager } from './WindChallengeManager';
const { ccclass, property } = _decorator;

@ccclass('PipeSpawner')
export class PipeSpawner extends Component {

    @property(Prefab)
    pipePrefab:Prefab = null;

    @property
    spawnRate:number = 3; // 基础生成间隔（轻松难度）

    // 不同难度的生成间隔倍率
    private readonly SPAWN_RATE_MULTIPLIER_EASY: number = 1.0;
    private readonly SPAWN_RATE_MULTIPLIER_NORMAL: number = 0.833; // 1/1.2
    private readonly SPAWN_RATE_MULTIPLIER_HARD: number = 0.667;   // 1/1.5

    private timer:number = 2;
    private _isSpawning:boolean = false;
    private _currentSpawnRate:number = 3; // 当前实际使用的生成间隔
    private _isWindMode:boolean = false; // 是否为大风吹模式

    update(deltaTime: number) {
        // 大风吹模式下不使用定时器生成
        if(this._isWindMode) return;

        if(this._isSpawning==false)return;
        this.timer += deltaTime;
        if(this.timer > this._currentSpawnRate){
            this.timer = 0;
            this.spawnPipe();
        }
    }

    /**
     * 生成管道的通用方法
     */
    private spawnPipe(): void {
        const pipeInst = instantiate(this.pipePrefab);
        this.node.addChild(pipeInst);
        const p = this.node.getWorldPosition();
        pipeInst.setWorldPosition(p);
        const y = math.randomRangeInt(-230,330);

        const pLoca = pipeInst.getPosition();
        pipeInst.setPosition(pLoca.x,y);

        // 根据当前难度决定是否添加MovingPipe组件
        const currentDifficulty = GameDifficulty.getDifficulty();

        // 根据难度添加MovingPipe组件
        if (currentDifficulty === GameDifficulty.DIFFICULTY_NORMAL ||
            currentDifficulty === GameDifficulty.DIFFICULTY_HARD) {
            // 在标准和困难难度下，添加MovingPipe组件
            // MovingPipe组件内部会处理上下移动的逻辑和概率
            if (!pipeInst.getComponent(MovingPipe)) {
                pipeInst.addComponent(MovingPipe);
            }
        }

        // 在大风吹模式下，设置管道的动态速度
        if (this._isWindMode) {
            const pipeComp = pipeInst.getComponent(Pipe);
            if (pipeComp) {
                (pipeComp as any).setWindMode(true);
            }
        }
    }

    public pause(){
        this._isSpawning=false;

        const nodeArray = this.node.children;
        for(let i=0;i<nodeArray.length;i++){
            const pipe = nodeArray[i].getComponent(Pipe);
            if(pipe){
                pipe.enabled = false;
            }

            // 同时禁用MovingPipe组件
            const movingPipe = nodeArray[i].getComponent(MovingPipe);
            if(movingPipe){
                movingPipe.enabled = false;
            }
        }
    }
    public start(){
        this._isSpawning = true;

        // 根据当前难度设置生成间隔
        this.updateSpawnRateByDifficulty();
    }

    // 根据当前难度更新生成间隔
    private updateSpawnRateByDifficulty() {
        const currentDifficulty = GameDifficulty.getDifficulty();

        // 根据难度设置生成间隔倍率
        let spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_EASY;

        switch(currentDifficulty) {
            case GameDifficulty.DIFFICULTY_NORMAL:
                spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_NORMAL;
                break;
            case GameDifficulty.DIFFICULTY_HARD:
                spawnRateMultiplier = this.SPAWN_RATE_MULTIPLIER_HARD;
                break;
        }

        // 计算实际生成间隔
        this._currentSpawnRate = this.spawnRate * spawnRateMultiplier;

        console.log(`管道生成间隔设置为: ${this._currentSpawnRate.toFixed(2)}秒 (难度: ${currentDifficulty})`);
    }

    /**
     * 启动大风吹模式
     */
    public startWindMode(): void {
        this._isWindMode = true;
        this._isSpawning = true;
        console.log("管道生成器：启动大风吹模式（距离控制）");
    }

    /**
     * 强制生成一个管道（用于大风吹模式的距离控制）
     */
    public forceSpawnPipe(): void {
        if (this._isWindMode && this._isSpawning) {
            this.spawnPipe();
            console.log("管道生成器：强制生成管道（大风吹模式）");
        }
    }

}


