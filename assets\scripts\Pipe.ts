import { _decorator, Component, Node } from 'cc';
import { GameManager } from './GameManager';
import { WindChallengeManager } from './WindChallengeManager';
const { ccclass, property } = _decorator;

@ccclass('Pipe')
export class Pipe extends Component {

    private moveSpeed:number = 100;
    private _isWindMode:boolean = false; // 是否为大风吹模式

    start() {
        // 使用基于当前难度的移动速度
        this.moveSpeed = GameManager.inst().getCurrentMoveSpeed();
    }

    update(deltaTime: number) {
        // 在大风吹模式下使用动态速度
        let currentSpeed = this.moveSpeed;
        if (this._isWindMode && WindChallengeManager.isWindMode()) {
            const windManager = WindChallengeManager.getInstance();
            if (windManager) {
                currentSpeed = windManager.getCurrentSpeed();
            }
        }

        const p = this.node.position;
        this.node.setPosition(p.x-currentSpeed*deltaTime,p.y);

        if(p.x<-900){
            this.node.destroy();
        }
    }

    /**
     * 设置大风吹模式
     */
    public setWindMode(isWindMode: boolean): void {
        this._isWindMode = isWindMode;
        console.log(`管道设置大风吹模式: ${isWindMode}`);
    }
}


