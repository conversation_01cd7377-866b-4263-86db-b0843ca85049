# 吸金石功能实现说明

## 功能概述

已成功实现吸金石功能，包括：
- 商店中购买吸金石（价格100金币）
- 使用/禁用吸金石
- 游戏中金币收集距离从50扩大到400
- 在所有游戏模式中应用吸金石效果

## 新增文件

### 1. `assets/scripts/UI/MagnetCard.ts`
- 吸金石UI控制器
- 处理购买、使用、禁用按钮的逻辑
- 显示金币不足提示

### 2. `assets/scripts/MagnetTest.ts`
- 吸金石功能测试类
- 包含完整的功能测试套件
- 可用于验证所有功能是否正常工作

## 修改的文件

### 1. `assets/scripts/ItemManager.ts`
- 新增 `ItemType.MAGNET` - 吸金石道具类型
- 新增吸金石配置（价格100金币）
- 新增 `isMagnetActive()` - 检查吸金石是否激活
- 新增 `getCoinCollectionDistance()` - 获取当前金币收集距离
- 修改 `activateItemEffect()` - 支持吸金石激活
- 修改 `deactivateItemEffect()` - 支持吸金石禁用
- 修改 `resetAllEffects()` - 重置时检查吸金石状态
- 修改 `consumeActiveItems()` - 游戏结束时消耗吸金石

### 2. `assets/scripts/Coin.ts`
- 导入 `ItemManager`
- 修改碰撞检测距离为动态获取
- 使用 `ItemManager.getCoinCollectionDistance()` 获取收集距离
- 添加调试日志显示收集距离和阈值

## Cocos Creator 手动配置清单

### 1. Shop场景配置

#### 在 `Shop.scene` 中的 `Magnet` 节点下：

1. **添加 MagnetCard 组件**
   - 选择 `Magnet` 节点
   - 在属性检查器中点击"添加组件"
   - 选择 `MagnetCard` 脚本

2. **配置 MagnetCard 组件属性**
   - `Purchase Button`: 拖拽 `PurchaseButton` 节点
   - `Use Button`: 拖拽 `UseButton` 节点
   - `Disable Button`: 拖拽 `DisableButton` 节点
   - `Price Label`: 拖拽 `PriceLabel` 节点
   - `Number Label`: 拖拽 `NumberLabel` 节点
   - `Insufficient Sprite`: 拖拽 `card_lack` 节点（金币不足提示）

3. **设置价格标签**
   - `PriceLabel` 的文本内容会自动设置为 "100"

4. **确保按钮位置一致**
   - `UseButton` 和 `DisableButton` 应该在相同位置
   - 只有一个会在同一时间显示

5. **配置金币不足提示**
   - 确保 `card_lack` 节点初始状态为隐藏 (active = false)
   - 使用 `card_lack.png` 作为 Sprite 组件的贴图

### 2. ItemPanel 配置

#### 在 `ItemPanel` 组件中：
- 确保 `Magnet` 节点已添加到 `itemNodes` 数组中
- 数组顺序应该是：[Double_coin, Magnet, ...]

### 3. 节点结构示例

```
ItemContainer
├── Double_coin (已存在)
│   ├── double_coin (图标)
│   ├── PurchaseButton
│   ├── UseButton
│   ├── DisableButton
│   ├── NumberLabel
│   ├── PriceLabel
│   ├── CoinIcon
│   └── card_lack
└── Magnet (需要配置)
    ├── magnet (图标，使用 magnet.png)
    ├── magnet_intro (介绍图片)
    ├── PurchaseButton
    ├── UseButton
    ├── DisableButton
    ├── NumberLabel
    ├── PriceLabel (显示 "100")
    ├── CoinIcon
    └── card_lack (金币不足提示)
```

## 功能说明

### 购买逻辑
- 点击购买按钮时检查金币是否足够（100金币）
- 如果金币足够，扣除金币并增加吸金石数量
- 如果金币不足，显示 `card_lack` 提示2秒

### 使用逻辑
- 点击使用按钮激活吸金石效果
- 金币收集距离从50扩大到400
- 隐藏使用按钮，显示禁用按钮
- 不消耗道具数量（游戏结束时才消耗）

### 禁用逻辑
- 点击禁用按钮取消吸金石效果
- 金币收集距离恢复到50
- 隐藏禁用按钮，显示使用按钮

### 游戏中效果
- 在所有游戏模式中生效（普通模式：轻松、标准、困难；挑战模式：大风吹、大雾起、大雪飘）
- 小鸟与金币距离在400以内即可自动收集
- 游戏结束时自动消耗一个吸金石（如果激活状态）

## 测试建议

1. **购买测试**
   - 金币充足时能正常购买
   - 金币不足时显示提示

2. **使用测试**
   - 有道具时能正常激活
   - 没有道具时显示提示

3. **游戏测试**
   - 激活吸金石后金币收集距离确实扩大
   - 在不同游戏模式下都能正常工作
   - 游戏结束后道具数量正确减少

4. **状态持久化测试**
   - 激活状态在场景切换后保持
   - 重启游戏后状态正确恢复

## 测试方法

### 自动测试
1. **添加测试组件**
   - 在任意场景中创建一个空节点
   - 添加 `MagnetTest` 组件
   - 运行游戏，查看控制台输出

2. **快速测试**
   - 在浏览器控制台中执行：
   ```javascript
   // 运行快速测试
   window.MagnetTest.runQuickTest();
   ```

### 手动测试
1. **商店界面测试**
   - 进入商店 → 道具面板 → 切换到吸金石
   - 测试购买、使用、禁用按钮功能
   - 验证金币不足提示

2. **游戏内测试**
   - 激活吸金石后进入游戏
   - 观察金币收集距离是否扩大
   - 验证在不同游戏模式下都生效

3. **持久化测试**
   - 激活吸金石 → 进入游戏 → 游戏结束 → 重新开始
   - 验证道具数量正确减少且状态保持
