# 吸金石配置清单

## 简化版配置说明

### 1. 添加组件
在Shop场景的`Magnet`节点上添加`MagnetCard`组件

### 2. 配置属性（共5个）
在`MagnetCard`组件中配置以下属性：

| 属性名 | 拖拽节点 | 说明 |
|--------|----------|------|
| `Purchase Button` | PurchaseButton | 购买按钮 |
| `Use Button` | UseButton | 使用按钮 |
| `Disable Button` | DisableButton | 禁用按钮 |
| `Price Label` | PriceLabel | 价格标签（自动显示2500） |
| `Insufficient Sprite` | card_lack | 金币不足提示 |

### 3. 重要设置
- ✅ UseButton和DisableButton位置要一致
- ✅ card_lack节点初始状态设为隐藏(active=false)
- ❌ 不需要NumberLabel（吸金石是唯一道具）

### 4. 节点结构参考
```
Magnet
├── magnet (图标)
├── magnet_intro (介绍图)
├── PurchaseButton (购买按钮)
├── UseButton (使用按钮)
├── DisableButton (禁用按钮)
├── PriceLabel (价格标签)
├── CoinIcon (金币图标)
└── card_lack (金币不足提示)
```

## 功能验证

配置完成后测试：

### 金币不足测试
1. 确保金币数量 < 2500
2. 点击购买按钮
3. 应该显示金币不足提示2秒

### 购买成功测试
1. 设置金币数量 ≥ 2500
2. 点击购买按钮
3. 应该：
   - 扣除2500金币
   - 隐藏购买按钮
   - 显示禁用按钮
   - 自动激活吸金石效果

### 使用/禁用测试
1. 购买后点击禁用按钮 → 显示使用按钮
2. 点击使用按钮 → 显示禁用按钮
3. 可以随时切换

### 游戏内测试
1. 激活吸金石后进入游戏
2. 观察金币收集距离是否扩大到400
3. 游戏结束后吸金石不会被消耗

## 完成标志

✅ 所有5个属性都已正确配置  
✅ 金币不足提示正常显示  
✅ 购买功能正常工作  
✅ 使用/禁用切换正常  
✅ 游戏内效果正常生效  

配置完成后，吸金石功能即可正常使用！
