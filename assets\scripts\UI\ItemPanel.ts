import { _decorator, Component, Node, Button } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ItemPanel')
export class ItemPanel extends Component {
    // 所有道具节点数组
    @property([Node])
    itemNodes: Node[] = [];

    // 左右切换按钮
    @property(Node)
    leftButton: Node = null;

    @property(Node)
    rightButton: Node = null;

    // 当前显示的道具索引
    private currentItemIndex: number = 0;

    start() {
        // 初始化：显示第一个道具，隐藏其他道具
        this.showItemAtIndex(this.currentItemIndex);

        // 检查按钮是否存在并有效
        if (this.leftButton && this.leftButton.isValid) {
            // 为左按钮添加点击事件
            const leftBtnComp = this.leftButton.getComponent(Button);
            if (leftBtnComp) {
                leftBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.leftButton.on(Button.EventType.CLICK, this.onLeftButtonClick, this);
            }
        }

        if (this.rightButton && this.rightButton.isValid) {
            // 为右按钮添加点击事件
            const rightBtnComp = this.rightButton.getComponent(Button);
            if (rightBtnComp) {
                rightBtnComp.clickEvents = [];
                // 添加按钮点击事件监听
                this.rightButton.on(Button.EventType.CLICK, this.onRightButtonClick, this);
            }
        }

        console.log("ItemPanel初始化完成，当前显示道具索引：", this.currentItemIndex);
    }

    onDestroy() {
        // 移除事件监听，防止内存泄漏
        if (this.leftButton && this.leftButton.isValid) {
            this.leftButton.off(Button.EventType.CLICK, this.onLeftButtonClick, this);
        }

        if (this.rightButton && this.rightButton.isValid) {
            this.rightButton.off(Button.EventType.CLICK, this.onRightButtonClick, this);
        }
    }

    // 显示指定索引的道具，隐藏其他道具
    showItemAtIndex(index: number) {
        if (this.itemNodes.length === 0) {
            console.error("ItemPanel: 没有配置道具节点！");
            return;
        }

        // 确保索引在有效范围内
        index = this.normalizeIndex(index);
        this.currentItemIndex = index;

        // 激活当前索引的道具，禁用其他道具
        for (let i = 0; i < this.itemNodes.length; i++) {
            this.itemNodes[i].active = (i === index);
        }

        console.log("ItemPanel: 切换到道具：", index);
    }

    // 处理索引循环（当索引超出范围时循环到另一端）
    normalizeIndex(index: number): number {
        const length = this.itemNodes.length;
        // 如果索引小于0，则循环到最后一个
        if (index < 0) {
            return length - 1;
        }
        // 如果索引超出范围，则循环到第一个
        if (index >= length) {
            return 0;
        }
        return index;
    }

    // 左按钮点击事件处理
    onLeftButtonClick() {
        console.log("ItemPanel: 点击左按钮");
        // 切换到上一个道具
        this.showItemAtIndex(this.currentItemIndex - 1);
    }

    // 右按钮点击事件处理
    onRightButtonClick() {
        console.log("ItemPanel: 点击右按钮");
        // 切换到下一个道具
        this.showItemAtIndex(this.currentItemIndex + 1);
    }
}
