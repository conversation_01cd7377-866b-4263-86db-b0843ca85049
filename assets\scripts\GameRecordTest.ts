import { _decorator, Component, Node } from 'cc';
import { GameData, GameMode } from './GameData';
const { ccclass, property } = _decorator;

/**
 * 游戏记录测试脚本
 * 用于测试六种游戏模式的独立记录功能
 */
@ccclass('GameRecordTest')
export class GameRecordTest extends Component {

    start() {
        // 延迟执行测试，确保GameData已初始化
        this.scheduleOnce(() => {
            this.runTests();
        }, 1.0);
    }

    private runTests() {
        console.log("=== 开始游戏记录系统测试 ===");

        // 显示当前记录状态
        console.log("\n--- 当前记录状态 ---");
        GameData.printAllGameRecords();

        // 询问是否要运行测试（这里只是输出提示，实际测试需要手动调用）
        console.log("\n如果要运行完整测试，请在控制台执行:");
        console.log("GameRecordTest.runFullTest()");

        console.log("=== 游戏记录系统已就绪 ===");
    }

    // 静态方法，可以从控制台手动调用
    public static runFullTest() {
        console.log("=== 开始完整测试 ===");

        const testInstance = new GameRecordTest();

        // 测试1: 验证当前记录状态
        testInstance.testCurrentRecords();

        // 测试2: 为每个模式设置不同的分数
        testInstance.testDifferentModeScores();

        // 测试3: 验证模式间的记录独立性
        testInstance.testModeIndependence();

        console.log("=== 完整测试完成 ===");
    }

    private testCurrentRecords() {
        console.log("\n--- 测试1: 验证当前记录状态 ---");

        for (let mode = 0; mode <= 5; mode++) {
            const gameMode = mode as GameMode;
            const modeName = GameData.getGameModeName(gameMode);
            const bestScore = GameData.getBestScore(gameMode);
            const topScores = GameData.getTopScores(gameMode);

            console.log(`${modeName}: 最高分=${bestScore}, 前三高分=[${topScores.join(', ')}]`);
            console.log(`✅ ${modeName} 记录读取正常`);
        }
    }

    private testDifferentModeScores() {
        console.log("\n--- 测试2: 为每个模式设置不同分数 ---");

        // 为每个模式设置不同的测试分数
        const testScores = [
            [10, 8, 6],    // 普通模式-轻松
            [20, 18, 16],  // 普通模式-标准
            [30, 28, 26],  // 普通模式-困难
            [40, 38, 36],  // 挑战模式-大风吹
            [50, 48, 46],  // 挑战模式-大雾起
            [60, 58, 56]   // 挑战模式-大雪飘
        ];

        for (let mode = 0; mode <= 5; mode++) {
            const gameMode = mode as GameMode;
            const modeName = GameData.getGameModeName(gameMode);
            const scores = testScores[mode];

            console.log(`设置 ${modeName} 的测试分数: [${scores.join(', ')}]`);

            // 设置当前模式
            GameData.setCurrentGameMode(gameMode);

            // 模拟三次游戏，设置不同分数
            for (const score of scores) {
                // 重置当前分数
                GameData.resetScore();
                // 添加分数
                for (let i = 0; i < score; i++) {
                    GameData.addScore(1);
                }
                // 保存分数
                GameData.saveScore(gameMode);
            }
        }

        console.log("所有模式的测试分数已设置完成");
    }

    private testModeIndependence() {
        console.log("\n--- 测试3: 验证模式间记录独立性 ---");

        const expectedScores = [
            [10, 8, 6],    // 普通模式-轻松
            [20, 18, 16],  // 普通模式-标准
            [30, 28, 26],  // 普通模式-困难
            [40, 38, 36],  // 挑战模式-大风吹
            [50, 48, 46],  // 挑战模式-大雾起
            [60, 58, 56]   // 挑战模式-大雪飘
        ];

        let allTestsPassed = true;

        for (let mode = 0; mode <= 5; mode++) {
            const gameMode = mode as GameMode;
            const modeName = GameData.getGameModeName(gameMode);
            const bestScore = GameData.getBestScore(gameMode);
            const topScores = GameData.getTopScores(gameMode);
            const expected = expectedScores[mode];

            console.log(`${modeName}:`);
            console.log(`  期望: 最高分=${expected[0]}, 前三高分=[${expected.join(', ')}]`);
            console.log(`  实际: 最高分=${bestScore}, 前三高分=[${topScores.join(', ')}]`);

            // 验证最高分
            if (bestScore !== expected[0]) {
                console.error(`  ❌ 最高分不匹配! 期望=${expected[0]}, 实际=${bestScore}`);
                allTestsPassed = false;
            }

            // 验证前三高分
            const scoresMatch = topScores.length === expected.length &&
                               topScores.every((score, index) => score === expected[index]);
            if (!scoresMatch) {
                console.error(`  ❌ 前三高分不匹配! 期望=[${expected.join(', ')}], 实际=[${topScores.join(', ')}]`);
                allTestsPassed = false;
            }

            if (bestScore === expected[0] && scoresMatch) {
                console.log(`  ✅ ${modeName} 记录验证通过`);
            }
        }

        if (allTestsPassed) {
            console.log("\n🎉 所有测试通过! 六种游戏模式的记录系统工作正常!");
        } else {
            console.error("\n❌ 部分测试失败! 请检查代码实现!");
        }

        // 最后打印所有记录
        console.log("\n--- 最终记录状态 ---");
        GameData.printAllGameRecords();
    }
}
