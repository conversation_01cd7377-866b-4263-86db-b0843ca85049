import { _decorator, Component, instantiate, Node, Prefab, UITransform, Vec3, view } from 'cc';
import { GameManager } from './GameManager';
import { SnowFlake } from './SnowFlake';
const { ccclass, property } = _decorator;

/**
 * 雪花生成器
 * 在大雪飘模式下随机生成雪花
 */
@ccclass('SnowSpawner')
export class SnowSpawner extends Component {
    // 雪花预制体
    @property(Prefab)
    snowFlakePrefab: Prefab = null;
    
    // 生成雪花的间隔时间范围（秒）
    @property
    minSpawnInterval: number = 0.2; // 最短生成间隔
    
    @property
    maxSpawnInterval: number = 1.0; // 最长生成间隔
    
    // 雪花下落速度范围
    @property
    minVerticalSpeed: number = 50;
    
    @property
    maxVerticalSpeed: number = 200;
    
    // 屏幕尺寸
    private viewWidth: number = 0;
    private viewHeight: number = 0;
    
    // 游戏管理器引用
    private gameManager: GameManager | null = null;
    
    // 下一次生成雪花的时间
    private nextSpawnTime: number = 0;
    
    // 是否正在生成雪花
    private _isSpawning: boolean = false;

    start() {
        // 获取屏幕尺寸
        const visibleSize = view.getVisibleSize();
        this.viewWidth = visibleSize.width;
        this.viewHeight = visibleSize.height;
        
        console.log("SnowSpawner: 屏幕尺寸 =", this.viewWidth, "x", this.viewHeight);
        
        // 获取游戏管理器引用
        this.gameManager = GameManager.inst();
        
        // 设置初次生成时间
        this.scheduleNextSpawn();
    }

    update(deltaTime: number) {
        // 如果不在生成状态，直接返回
        if (!this._isSpawning) return;
        
        // 累计时间
        this.nextSpawnTime -= deltaTime;
        
        // 当达到生成时间时，生成1-2朵雪花
        if (this.nextSpawnTime <= 0) {
            // 随机决定生成多少朵雪花（1-2朵）
            const flakeCount = Math.floor(Math.random() * 2) + 1;
            
            // 生成雪花
            for (let i = 0; i < flakeCount; i++) {
                this.spawnSnowFlake();
            }
            
            // 安排下一次生成
            this.scheduleNextSpawn();
        }
    }
    
    /**
     * 开始生成雪花
     */
    public startSpawning() {
        // 防止重复启动
        if (this._isSpawning) {
            console.log("雪花生成器已经在运行中，忽略重复启动");
            return;
        }
        
        this._isSpawning = true;
        console.log("雪花生成器已启动");
    }
    
    /**
     * 暂停生成雪花
     */
    public pause() {
        this._isSpawning = false;
        
        // 禁用所有雪花的移动
        const snowFlakes = this.node.children;
        for (let i = 0; i < snowFlakes.length; i++) {
            const snowFlake = snowFlakes[i].getComponent(SnowFlake);
            if (snowFlake) {
                snowFlake.enabled = false;
            }
        }
        
        console.log("雪花生成器已暂停");
    }
    
    /**
     * 安排下一次雪花生成时间
     */
    private scheduleNextSpawn() {
        // 随机生成间隔时间
        const interval = this.minSpawnInterval + Math.random() * (this.maxSpawnInterval - this.minSpawnInterval);
        this.nextSpawnTime = interval;
    }
    
    /**
     * 生成单个雪花
     */
    private spawnSnowFlake() {
        if (!this.snowFlakePrefab) {
            console.error("雪花预制体未设置！");
            return;
        }
        
        // 实例化雪花预制体
        const snowFlakeNode = instantiate(this.snowFlakePrefab);
        
        // 随机横坐标位置（只在屏幕最右边1/3范围生成）
        // 屏幕最右边1/3的起始位置：viewWidth * 2/3
        // 最右边1/3的宽度：viewWidth / 3
        const rightThirdStart = this.viewWidth * 2 / 3;
        const rightThirdWidth = this.viewWidth / 3;
        const randomX = rightThirdStart + Math.random() * rightThirdWidth;
        
        // 设置初始位置在屏幕顶部
        const startY = this.viewHeight / 2 + 100; // 从屏幕顶部稍微上方开始
        snowFlakeNode.setPosition(randomX, startY, 0);
        
        // 添加到当前节点下
        this.node.addChild(snowFlakeNode);
        
        // 确保雪花节点激活
        snowFlakeNode.active = true;
        
        console.log("生成雪花: 位置 =", randomX.toFixed(1), startY, "(屏幕最右边1/3范围)");
        console.log("屏幕宽度:", this.viewWidth, "最右边1/3范围:", rightThirdStart.toFixed(1), "到", (rightThirdStart + rightThirdWidth).toFixed(1));
        
        // 获取雪花组件
        const snowFlakeComp = snowFlakeNode.getComponent(SnowFlake);
        if (snowFlakeComp) {
            // 设置水平速度与管道移动速度相同
            const horizontalSpeed = this.gameManager ? this.gameManager.getCurrentMoveSpeed() : 100;
            
            // 随机垂直速度
            const verticalSpeed = this.minVerticalSpeed + Math.random() * (this.maxVerticalSpeed - this.minVerticalSpeed);
            
            console.log("雪花速度: 水平 =", horizontalSpeed, "垂直 =", verticalSpeed.toFixed(1));
            
            // 初始化雪花
            snowFlakeComp.init(horizontalSpeed, verticalSpeed);
        } else {
            console.error("雪花预制体缺少SnowFlake组件！");
        }
    }
}


