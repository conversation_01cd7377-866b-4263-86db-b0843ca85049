import { _decorator, Component, director, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 体力值管理器，负责体力值的存储、消耗、恢复等逻辑
 */
@ccclass('EnergyManager')
export class EnergyManager extends Component {
    // 单例实例
    private static _instance: EnergyManager = null;

    // 存储键
    private static readonly ENERGY_KEY: string = "PlayerEnergy";
    private static readonly ENERGY_RECOVER_TIME_KEY: string = "EnergyRecoverTime";

    // 体力值相关常量
    public static readonly MAX_ENERGY: number = 100;
    public static readonly ENERGY_PER_GAME: number = 1;
    public static readonly RECOVER_MINUTES: number = 1; // 恢复1点体力需要的分钟数

    // 当前体力值
    private _currentEnergy: number = EnergyManager.MAX_ENERGY;
    // 下一次恢复体力的时间戳
    private _nextRecoverTime: number = 0;
    // 是否已初始化
    private _initialized: boolean = false;

    // 事件回调
    private _onEnergyChangeCallbacks: Function[] = [];

    onLoad() {
        // 单例模式
        if (EnergyManager._instance) {
            this.node.destroy();
            return;
        }

        EnergyManager._instance = this;

        // 只有当节点是场景根节点的直接子节点时，才能设置为持久节点
        if (this.node.parent === director.getScene()) {
            director.addPersistRootNode(this.node);
            console.log("EnergyManager: 节点已设置为持久节点");
        } else {
            console.log("EnergyManager: 节点不是场景根节点的直接子节点，无法设置为持久节点");
            // 不尝试将节点设置为持久节点，避免报错
        }

        // 初始化体力值
        this.initialize();

        // 添加场景切换事件监听
        director.on('after-scene-launch', this.onSceneChanged, this);
    }

    onDestroy() {
        // 移除场景切换事件监听
        director.off('after-scene-launch', this.onSceneChanged, this);
    }

    /**
     * 场景切换事件处理
     */
    private onSceneChanged() {
        console.log("场景切换，强制检查体力恢复");

        // 确保已初始化
        if (!this._initialized) {
            this.initialize();
        }

        // 场景切换后强制检查一次体力恢复
        const now = Date.now();
        const timeUntilRecover = this._nextRecoverTime > 0 ? (this._nextRecoverTime - now) : 0;
        console.log(`场景切换检查: 当前体力=${this._currentEnergy}, 恢复时间=${EnergyManager.formatTimeRemaining(timeUntilRecover)}`);

        // 如果恢复时间已到或接近恢复时间，强制触发恢复
        if (this._nextRecoverTime > 0 && timeUntilRecover <= 1000) { // 如果小于1秒
            console.log("场景切换时发现体力即将恢复，强制恢复");
            this._nextRecoverTime = now - 10; // 设置为已过期
        }

        // 检查体力恢复
        this.checkEnergyRecover();

        // 通知所有UI刷新
        this.notifyEnergyChange();
    }

    start() {
        // 确保已初始化
        if (!this._initialized) {
            this.initialize();
        }
    }

    // 更新计时器
    private _updateTimer: number = 0;
    // 更新间隔（秒）
    private readonly UPDATE_INTERVAL: number = 1.0;

    update(deltaTime: number) {
        // 累加计时器
        this._updateTimer += deltaTime;

        // 每隔一定时间检查一次体力值恢复
        if (this._updateTimer >= this.UPDATE_INTERVAL) {
            this._updateTimer = 0;
            this.checkEnergyRecover();

            // 调试日志，检查update是否在场景中被调用
            if (this._nextRecoverTime > 0) {
                const timeUntilRecover = this.getTimeUntilNextRecover();
                const totalSeconds = Math.round(timeUntilRecover / 1000);
                //console.log(`EnergyManager.update - 剩余时间: ${EnergyManager.formatTimeRemaining(timeUntilRecover)}, 秒数: ${totalSeconds}`);
            }
        }
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): EnergyManager {
        return this._instance;
    }

    /**
     * 初始化体力值系统
     */
    private initialize() {
        if (this._initialized) return;

        // 从本地存储加载体力值
        const savedEnergy = localStorage.getItem(EnergyManager.ENERGY_KEY);
        if (savedEnergy !== null) {
            this._currentEnergy = parseInt(savedEnergy);
        } else {
            this._currentEnergy = EnergyManager.MAX_ENERGY;
            this.saveEnergy();
        }

        // 从本地存储加载下一次恢复时间
        const savedRecoverTime = localStorage.getItem(EnergyManager.ENERGY_RECOVER_TIME_KEY);
        if (savedRecoverTime !== null) {
            this._nextRecoverTime = parseInt(savedRecoverTime);
        } else {
            this._nextRecoverTime = 0;
        }

        this._initialized = true;
        console.log(`体力系统初始化完成，当前体力: ${this._currentEnergy}/${EnergyManager.MAX_ENERGY}`);

        // 立即检查一次体力恢复
        this.checkEnergyRecover();
    }

    /**
     * 强制检查体力值恢复
     * 公开方法，可以从外部调用
     */
    public forceCheckEnergyRecover() {
        // 如果当前时间接近恢复时间，则强制设置为已到达恢复时间
        if (this._nextRecoverTime > 0) {
            const now = Date.now();
            const timeUntilRecover = this._nextRecoverTime - now;

            // 计算剩余秒数
            const totalSeconds = Math.round(timeUntilRecover / 1000);

            // 输出调试信息
            //console.log(`forceCheckEnergyRecover - 剩余秒数: ${totalSeconds}, 毫秒: ${timeUntilRecover}`);

            // 判断是否为0秒（新需求：在00:00时触发恢复）
            if (totalSeconds <= 0) {
                console.log("强制触发体力恢复检查，剩余时间: 0秒");
                // 设置为已过期
                this._nextRecoverTime = now - 10;
                this.checkEnergyRecover();

                // 通知体力值变化，确保所有UI都会更新
                this.notifyEnergyChange();
            }
        }
    }

    /**
     * 检查体力值恢复
     * 私有方法，由系统定期调用
     */
    private checkEnergyRecover() {
        // 如果体力已满，不需要恢复
        if (this._currentEnergy >= EnergyManager.MAX_ENERGY) {
            this._nextRecoverTime = 0;
            this.saveRecoverTime();
            return;
        }

        const now = Date.now();

        // 如果没有设置恢复时间，设置为当前时间加上恢复间隔
        if (this._nextRecoverTime === 0) {
            this._nextRecoverTime = now + EnergyManager.RECOVER_MINUTES * 60 * 1000;
            this.saveRecoverTime();
            this.notifyEnergyChange();
            return;
        }

        // 如果已经到了恢复时间
        if (now >= this._nextRecoverTime) {
            // 恢复1点体力
            const recoverPoints = 1;

            // 确保不超过最大体力值
            if (this._currentEnergy < EnergyManager.MAX_ENERGY) {
                console.log(`恢复体力 ${recoverPoints} 点，当前: ${this._currentEnergy} -> ${this._currentEnergy + recoverPoints}`);

                // 恢复体力
                this._currentEnergy += recoverPoints;
                if (this._currentEnergy > EnergyManager.MAX_ENERGY) {
                    this._currentEnergy = EnergyManager.MAX_ENERGY;
                }

                // 保存体力值
                this.saveEnergy();

                // 更新下一次恢复时间
                if (this._currentEnergy < EnergyManager.MAX_ENERGY) {
                    // 设置下一次恢复时间为当前时间加上恢复间隔
                    this._nextRecoverTime = now + EnergyManager.RECOVER_MINUTES * 60 * 1000;
                    console.log(`设置下一次体力恢复时间: ${new Date(this._nextRecoverTime).toLocaleTimeString()}, 间隔: ${EnergyManager.RECOVER_MINUTES}分钟`);
                } else {
                    this._nextRecoverTime = 0;
                    console.log(`体力已满，重置恢复时间`);
                }
                this.saveRecoverTime();

                // 通知体力值变化
                this.notifyEnergyChange();
            } else {
                // 体力已满，重置恢复时间
                this._nextRecoverTime = 0;
                this.saveRecoverTime();
            }
        } else {
            // 检查恢复时间是否异常（可能是由于场景切换导致的计时器暂停）
            const timeUntilRecover = this._nextRecoverTime - now;
            // 恢复时间过长（超过正常恢复间隔的2倍）可能是计时器异常
            if (timeUntilRecover > EnergyManager.RECOVER_MINUTES * 60 * 1000 * 2) {
                console.warn(`检测到异常恢复时间，重置: ${timeUntilRecover/1000}秒`);
                // 重置为正常恢复时间
                this._nextRecoverTime = now + EnergyManager.RECOVER_MINUTES * 60 * 1000;
                this.saveRecoverTime();
                this.notifyEnergyChange();
            }
        }
    }

    /**
     * 消耗体力值
     * @param amount 消耗的体力值数量
     * @returns 是否成功消耗
     */
    public consumeEnergy(amount: number = EnergyManager.ENERGY_PER_GAME): boolean {
        // 确保已初始化
        if (!this._initialized) {
            this.initialize();
        }

        // 检查体力是否足够
        if (this._currentEnergy < amount) {
            console.log(`体力不足，当前体力: ${this._currentEnergy}，需要: ${amount}`);
            return false;
        }

        // 消耗体力
        this._currentEnergy -= amount;
        console.log(`消耗体力 ${amount} 点，剩余: ${this._currentEnergy}`);

        // 保存体力值
        this.saveEnergy();

        // 如果这是第一次体力不满，设置恢复时间
        if (this._nextRecoverTime === 0 && this._currentEnergy < EnergyManager.MAX_ENERGY) {
            this._nextRecoverTime = Date.now() + EnergyManager.RECOVER_MINUTES * 60 * 1000;
            this.saveRecoverTime();
        }

        // 通知体力值变化
        this.notifyEnergyChange();

        return true;
    }

    /**
     * 获取当前体力值
     */
    public getCurrentEnergy(): number {
        return this._currentEnergy;
    }

    /**
     * 获取最大体力值
     */
    public getMaxEnergy(): number {
        return EnergyManager.MAX_ENERGY;
    }

    /**
     * 获取下一次恢复体力的时间戳
     */
    public getNextRecoverTime(): number {
        return this._nextRecoverTime;
    }

    /**
     * 获取距离下一次恢复体力的剩余时间（毫秒）
     */
    public getTimeUntilNextRecover(): number {
        if (this._nextRecoverTime === 0 || this._currentEnergy >= EnergyManager.MAX_ENERGY) {
            return 0;
        }

        const now = Date.now();
        return Math.max(0, this._nextRecoverTime - now);
    }

    /**
     * 保存体力值到本地存储
     */
    private saveEnergy() {
        localStorage.setItem(EnergyManager.ENERGY_KEY, this._currentEnergy.toString());
    }

    /**
     * 保存恢复时间到本地存储
     */
    private saveRecoverTime() {
        localStorage.setItem(EnergyManager.ENERGY_RECOVER_TIME_KEY, this._nextRecoverTime.toString());
    }

    /**
     * 添加体力值变化的回调函数
     * @param callback 回调函数
     */
    public addEnergyChangeCallback(callback: Function) {
        // 确保回调数组已初始化
        if (!this._onEnergyChangeCallbacks) {
            this._onEnergyChangeCallbacks = [];
        }
        this._onEnergyChangeCallbacks.push(callback);
    }

    /**
     * 移除体力值变化的回调函数
     * @param callback 回调函数
     */
    public removeEnergyChangeCallback(callback: Function) {
        // 确保回调数组已初始化
        if (!this._onEnergyChangeCallbacks) {
            return;
        }

        const index = this._onEnergyChangeCallbacks.indexOf(callback);
        if (index !== -1) {
            this._onEnergyChangeCallbacks.splice(index, 1);
        }
    }

    /**
     * 通知体力值变化
     */
    private notifyEnergyChange() {
        // 确保回调数组存在且可迭代
        if (this._onEnergyChangeCallbacks && Array.isArray(this._onEnergyChangeCallbacks)) {
            // 使用传统的for循环，更安全
            for (let i = 0; i < this._onEnergyChangeCallbacks.length; i++) {
                try {
                    const callback = this._onEnergyChangeCallbacks[i];
                    if (typeof callback === 'function') {
                        callback();
                    }
                } catch (error) {
                    console.error("执行体力变化回调时出错:", error);
                }
            }
        }
    }

    /**
     * 格式化剩余时间为 MM:SS 格式
     * @param milliseconds 剩余的毫秒数
     */
    public static formatTimeRemaining(milliseconds: number): string {
        if (milliseconds <= 0) {
            return "00:00";
        }

        // 使用Math.round而不是Math.ceil，以便更精确地显示时间
        const totalSeconds = Math.round(milliseconds / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;

        // 使用手动补零方法替代padStart
        const minutesStr = minutes < 10 ? "0" + minutes : minutes.toString();
        const secondsStr = seconds < 10 ? "0" + seconds : seconds.toString();

        // 输出调试信息
        //console.log(`formatTimeRemaining - 毫秒: ${milliseconds}, 格式化: ${minutesStr}:${secondsStr}`);

        return `${minutesStr}:${secondsStr}`;
    }
}

