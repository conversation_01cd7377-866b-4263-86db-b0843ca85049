import { _decorator, Component, director, Node } from 'cc';
import { GameDifficulty } from '../GameDifficulty';
import { AudioMgr } from '../AudioMgr';
import { ChallengeMode, ChallengeModeType } from '../ChallengeMode';
const { ccclass, property } = _decorator;

@ccclass('ChallengeUI')
export class ChallengeUI extends Component {
    // 在Cocos Creator中，组件会自动附加到节点上，并可通过this.node访问
    // 这个属性在运行时由引擎提供
    declare readonly node: Node;

    @property(Node)
    mainPanel: Node = null;

    // 挑战模式的类型（可以根据需要扩展）
    private static readonly CHALLENGE_MODE_1: number = 1;
    private static readonly CHALLENGE_MODE_2: number = 2;
    private static readonly CHALLENGE_MODE_3: number = 3;

    start() {
        // 初始化
    }

    // 点击挑战模式1按钮 - 大风起
    onChallengeMode1BtnClick() {
        // 设置挑战模式1 - 大风起
        this.setChallengeMode(ChallengeUI.CHALLENGE_MODE_1);

        // 设置挑战模式类型
        ChallengeMode.setMode(ChallengeModeType.WIND);

        // 设置困难难度（大风吹基于困难难度）
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_HARD);

        // 停止主菜单背景音乐，避免与游戏场景的音乐重叠
        AudioMgr.inst.stop();

        // 加载游戏场景
        director.loadScene('Game');
    }

    // 点击挑战模式2按钮 - 大雾起
    onChallengeMode2BtnClick() {
        // 设置挑战模式2 - 大雾起
        this.setChallengeMode(ChallengeUI.CHALLENGE_MODE_2);

        // 设置挑战模式类型
        ChallengeMode.setMode(ChallengeModeType.FOG);

        // 设置标准难度
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_NORMAL);

        // 停止主菜单背景音乐，避免与游戏场景的音乐重叠
        AudioMgr.inst.stop();

        // 加载游戏场景
        director.loadScene('Game');
    }

    // 点击挑战模式3按钮 - 大雪起
    onChallengeMode3BtnClick() {
        // 设置挑战模式3 - 大雪起
        this.setChallengeMode(ChallengeUI.CHALLENGE_MODE_3);

        // 设置挑战模式类型
        ChallengeMode.setMode(ChallengeModeType.SNOW);

        // 设置标准难度
        GameDifficulty.setDifficulty(GameDifficulty.DIFFICULTY_NORMAL);

        // 停止主菜单背景音乐，避免与游戏场景的音乐重叠
        AudioMgr.inst.stop();

        // 加载游戏场景
        director.loadScene('Game');
    }

    // 设置挑战模式
    private setChallengeMode(mode: number) {
        // 记录原始的挑战模式编号（兼容原有代码）
        console.log(`设置挑战模式: ${mode}`);

        // 注意：具体的挑战模式类型设置已移动到各个按钮点击方法中
        // 这里不再需要设置难度，因为每个挑战模式的难度已在各自的点击方法中设置

        // 兼容原有的本地存储方式（可选）
        localStorage.setItem('challengeMode', mode.toString());
    }

    // 点击返回按钮
    onBackBtnClick() {
        // 隐藏挑战选择面板，显示主面板
        this.node.active = false;
        this.mainPanel.active = true;
    }
}
