import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 体力值耗尽提示面板
 */
@ccclass('EnergyExhaustedPanel')
export class EnergyExhaustedPanel extends Component {

    start() {
        // 不在初始化时隐藏面板，让它保持编辑器中设置的状态
        // this.node.active = false;
        console.log("EnergyExhaustedPanel 初始化");
    }

    /**
     * 显示面板
     */
    public show(): void {
        // 显示面板
        this.node.active = true;
    }

    /**
     * 隐藏面板
     */
    public hide(): void {
        // 隐藏面板
        this.node.active = false;

        // 输出日志，便于调试
        console.log("EnergyExhaustedPanel: 面板已隐藏");
    }

    /**
     * 关闭按钮点击事件处理
     * 在编辑器中将此方法绑定到关闭按钮的点击事件
     */
    public onCloseButtonClick(): void {
        console.log("EnergyExhaustedPanel: 关闭按钮被点击");

        // 隐藏面板
        this.hide();

        // 特殊处理：尝试重置GameReadyUI的_touchProcessed标志
        // 这是一个hack，但可能有效
        this.scheduleOnce(() => {
            // 查找GameReadyUI节点
            const gameReadyUI = this.node.parent.getChildByName("GameReadyUI");
            if (gameReadyUI) {
                // 获取GameReadyUI组件
                const gameReadyUIComp = gameReadyUI.getComponent("GameReadyUI");
                if (gameReadyUIComp && gameReadyUIComp["_touchProcessed"] !== undefined) {
                    // 重置_touchProcessed标志
                    gameReadyUIComp["_touchProcessed"] = false;
                    console.log("EnergyExhaustedPanel: 已重置GameReadyUI的_touchProcessed标志");
                }
            }
        }, 0.1);
    }
}
