import { _decorator, Component, Node } from 'cc';
import { BirdType, GameData } from './GameData';
import { Bird } from './Bird';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

@ccclass('BirdSelector')
export class BirdSelector extends Component {
    @property(Node)
    birdNormal: Node = null;

    @property(Node)
    birdGold: Node = null;

    @property(Node)
    birdPenguin: Node = null;

    @property(Node)
    birdAlbatross: Node = null;

    @property(Node)
    birdWoodpecker: Node = null;

    // 所有小鸟节点数组
    private birdNodes: Node[] = [];

    start() {
        // 将所有小鸟节点添加到数组中
        this.birdNodes = [
            this.birdNormal,
            this.birdGold,
            this.birdPenguin,
            this.birdAlbatross,
            this.birdWoodpecker
        ];

        // 获取保存的小鸟类型
        const selectedBirdType = GameData.getSelectedBirdType();
        console.log(`游戏场景加载，选择的小鸟类型: ${selectedBirdType}`);

        // 激活选中的小鸟，禁用其他小鸟
        this.activateSelectedBird(selectedBirdType);
        
        // 延迟一帧设置GameManager的bird引用，确保小鸟节点已经激活
        this.scheduleOnce(() => {
            this.updateGameManagerBirdReference();
        }, 0);
    }

    /**
     * 激活选择的小鸟，禁用其他小鸟
     * @param birdType 要激活的小鸟类型
     */
    private activateSelectedBird(birdType: BirdType) {
        // 遍历所有小鸟节点
        for (let i = 0; i < this.birdNodes.length; i++) {
            const node = this.birdNodes[i];
            if (node) {
                // 设置节点激活状态
                node.active = (i === birdType);
                
                // 确保小鸟组件的控制功能被正确禁用
                if (i === birdType) {
                    const birdComponent = node.getComponent(Bird);
                    if (birdComponent) {
                        // 如果是当前选中的小鸟，先禁用其控制功能
                        // 这样在游戏准备界面小鸟不会下落
                        birdComponent.disableControl();
                    }
                }
                
                console.log(`小鸟 ${i} 激活状态: ${node.active}`);
            }
        }
    }
    
    /**
     * 更新GameManager中的bird引用为当前选择的小鸟
     */
    private updateGameManagerBirdReference() {
        const gameManager = GameManager.inst();
        if (!gameManager) {
            console.error('无法获取GameManager实例');
            return;
        }
        
        const selectedBirdType = GameData.getSelectedBirdType();
        const selectedBirdNode = this.birdNodes[selectedBirdType];
        
        if (selectedBirdNode && selectedBirdNode.isValid) {
            const birdComponent = selectedBirdNode.getComponent(Bird);
            if (birdComponent) {
                // 更新GameManager中的bird引用
                gameManager.bird = birdComponent;
                console.log(`已更新GameManager的bird引用为小鸟类型: ${selectedBirdType}`);
            } else {
                console.error(`选中的小鸟节点没有Bird组件`);
            }
        } else {
            console.error(`无法获取选中的小鸟节点`);
        }
    }
}
