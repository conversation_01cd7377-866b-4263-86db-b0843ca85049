import { _decorator, Component, Node, UIOpacity } from 'cc';
import { ChallengeMode, ChallengeModeType } from './ChallengeMode';
const { ccclass, property } = _decorator;

/**
 * 大雾效果控制组件
 * 用于控制大雾挑战模式下的雾效果显示
 */
@ccclass('FogEffect')
export class FogEffect extends Component {
    @property(UIOpacity)
    fogOpacity: UIOpacity = null;

    @property
    defaultOpacity: number = 180; // 默认透明度值（0-255）

    start() {
        // 初始化时默认隐藏雾效果
        this.hideFogEffect();
        
        // 然后检查是否为大雾模式
        this.checkFogMode();
    }

    /**
     * 检查是否为大雾模式，并相应显示或隐藏雾效果
     */
    checkFogMode() {
        // 获取当前挑战模式
        const currentMode = ChallengeMode.getMode();
        
        // 如果是大雾模式，显示雾效果
        if (currentMode === ChallengeModeType.FOG) {
            console.log("大雾模式已激活，显示雾效果");
            this.showFogEffect();
        } else {
            // 否则隐藏雾效果
            this.hideFogEffect();
        }
    }

    /**
     * 显示雾效果
     */
    showFogEffect() {
        // 确保节点激活
        this.node.active = true;
        
        // 如果有设置透明度组件，则设置透明度
        if (this.fogOpacity) {
            this.fogOpacity.opacity = this.defaultOpacity;
        }
    }

    /**
     * 隐藏雾效果
     */
    hideFogEffect() {
        // 直接禁用节点
        this.node.active = false;
    }
}
