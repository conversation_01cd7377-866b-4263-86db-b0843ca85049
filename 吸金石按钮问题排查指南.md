# 吸金石按钮问题排查指南

## 问题描述
点击吸金石的PurchaseButton没有任何反应，按钮甚至没有SCALE动画效果。

## 排查步骤

### 1. 检查MagnetCard组件是否正确添加
1. 选择Shop场景中的`Magnet`节点
2. 在属性检查器中查看是否有`MagnetCard`组件
3. 如果没有，点击"添加组件" → 选择`MagnetCard`

### 2. 检查按钮引用是否正确配置
在`MagnetCard`组件中检查：
- `Purchase Button`是否拖拽了正确的PurchaseButton节点
- `Use Button`是否拖拽了正确的UseButton节点
- `Disable Button`是否拖拽了正确的DisableButton节点
- `Price Label`是否拖拽了正确的PriceLabel节点
- `Insufficient Sprite`是否拖拽了正确的card_lack节点

### 3. 检查控制台日志
运行游戏后查看控制台是否有以下日志：
```
=== 设置吸金石按钮事件 ===
绑定购买按钮事件
绑定使用按钮事件
绑定禁用按钮事件
MagnetCard UI 初始化完成
```

如果看到错误日志如"购买按钮引用为空！"，说明按钮引用没有正确配置。

### 4. 检查按钮节点本身
1. 选择PurchaseButton节点
2. 确认它有Button组件
3. 确认Button组件的Interactable是勾选的
4. 确认Transition设置为SCALE
5. 确认Target是按钮自身或子节点

### 5. 测试按钮点击
点击购买按钮后，控制台应该显示：
```
=== 点击购买吸金石 ===
当前金币: [数量], 需要金币: 2500
金币不足，显示提示
```

### 6. 检查节点层级和遮挡
1. 确认PurchaseButton节点在正确的层级
2. 确认没有其他透明节点遮挡按钮
3. 确认按钮的Size和Position正确

## 常见问题和解决方案

### 问题1: 组件没有添加
**症状**: 没有任何日志输出
**解决**: 给Magnet节点添加MagnetCard组件

### 问题2: 按钮引用错误
**症状**: 控制台显示"购买按钮引用为空！"
**解决**: 重新拖拽正确的按钮节点到组件属性中

### 问题3: 按钮被禁用
**症状**: 按钮灰色，无法点击
**解决**: 检查Button组件的Interactable是否勾选

### 问题4: 事件没有绑定
**症状**: 有初始化日志但点击无反应
**解决**: 检查按钮引用是否正确，重新配置组件

### 问题5: 节点被遮挡
**症状**: 按钮看起来正常但点击无效
**解决**: 检查是否有其他节点遮挡，调整层级

## 调试代码
我已经在MagnetCard.ts中添加了详细的调试日志，运行游戏后查看控制台输出可以帮助定位问题。

## 快速验证方法
1. 运行游戏
2. 进入商店 → 道具面板 → 切换到吸金石
3. 查看控制台日志
4. 点击购买按钮
5. 查看是否有点击日志输出

如果按照以上步骤仍然无法解决，请提供：
1. 控制台的完整日志输出
2. MagnetCard组件的属性配置截图
3. Magnet节点的层级结构截图
