import { _decorator, Component, Node, director } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 挑战模式类型枚举
 */
export enum ChallengeModeType {
    NONE = 0,
    WIND = 1,  // 大风起
    FOG = 2,   // 大雾起
    SNOW = 3   // 大雪起
}

/**
 * 挑战模式管理类
 */
@ccclass('ChallengeMode')
export class ChallengeMode extends Component {
    // 当前挑战模式类型
    private static _currentMode: ChallengeModeType = ChallengeModeType.NONE;
    
    // 挑战模式存储键
    private static readonly CHALLENGE_MODE_KEY: string = "ChallengeMode";
    
    // 静态初始化方法，确保在游戏启动时重置挑战模式
    static {
        // 清除挑战模式
        ChallengeMode.clearMode();
        console.log("游戏启动，重置挑战模式");
    }
    
    /**
     * 设置当前挑战模式
     * @param mode 挑战模式类型
     */
    public static setMode(mode: ChallengeModeType): void {
        this._currentMode = mode;
        // 保存到本地存储
        localStorage.setItem(this.CHALLENGE_MODE_KEY, mode.toString());
        console.log(`设置挑战模式: ${mode}`);
    }
    
    /**
     * 获取当前挑战模式
     * @returns 当前挑战模式类型
     */
    public static getMode(): ChallengeModeType {
        // 尝试从本地存储获取
        const savedMode = localStorage.getItem(this.CHALLENGE_MODE_KEY);
        if (savedMode !== null) {
            return parseInt(savedMode) as ChallengeModeType;
        }
        return this._currentMode;
    }
    
    /**
     * 清除挑战模式
     */
    public static clearMode(): void {
        this._currentMode = ChallengeModeType.NONE;
        localStorage.removeItem(this.CHALLENGE_MODE_KEY);
        console.log("清除挑战模式");
    }
    
    /**
     * 检查是否为特定挑战模式
     * @param mode 要检查的挑战模式类型
     * @returns 是否为指定的挑战模式
     */
    public static isMode(mode: ChallengeModeType): boolean {
        return this.getMode() === mode;
    }
    
    /**
     * 检查是否为大雾模式
     * @returns 是否为大雾模式
     */
    public static isFogMode(): boolean {
        return this.isMode(ChallengeModeType.FOG);
    }
}
