import { _decorator, Component, Node, AudioClip, asset<PERSON>anager, director } from 'cc';
import { AudioMgr } from '../AudioMgr';
const { ccclass, property } = _decorator;

@ccclass('HomeUI')
export class HomeUI extends Component {
    @property(Node)
    mainPanel: Node = null;

    @property(Node)
    difficultyPanel: Node = null;

    @property(Node)
    challengePanel: Node = null;

    // 重新使用直接引用，但确保在运行时正确处理
    @property(AudioClip)
    bgAudio: AudioClip = null;

    onLoad() {
        console.log("HomeUI onLoad");

        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            console.log("bgAudio已设置，准备播放主菜单BGM");
            // 确保在场景加载时立即播放主菜单BGM
            this.playHomeBGM();
        } else {
            //console.error("bgAudio未设置！请在编辑器中将home_bgm.mp3拖拽到HomeUI组件的bgAudio属性中");
            // 尝试在运行时获取bgAudio
            this.tryGetBgAudio();
            // 注意：tryGetBgAudio成功后会自动调用playHomeBGM
        }
    }

    start() {
        // 初始显示主面板，隐藏难度选择面板
        this.mainPanel.active = true;
        this.difficultyPanel.active = false;

        // 再次检查bgAudio是否已设置
        if (!this.bgAudio) {
            console.log("start: bgAudio仍未设置，再次尝试获取");
            this.tryGetBgAudio();
        }
    }

    // 尝试在运行时获取bgAudio
    tryGetBgAudio() {
        // 这是一个备用方案，尝试通过UUID直接获取音频资源
        // 这是home_bgm.mp3的UUID，从meta文件中获取
        const HOME_BGM_UUID = "2d384416-f267-40be-9fb8-f091110d84c6";

        try {
            // 尝试通过UUID直接加载音频资源
            console.log("尝试通过UUID加载主菜单BGM:", HOME_BGM_UUID);
            assetManager.loadAny({uuid: HOME_BGM_UUID}, (err, asset) => {
                if (err) {
                    console.error("通过UUID加载主菜单BGM失败:", err);
                    return;
                }

                console.log("通过UUID加载主菜单BGM成功");
                this.bgAudio = asset as AudioClip;

                // 加载成功后立即播放
                this.playHomeBGM();
            });
        } catch (error) {
            console.error("获取bgAudio失败:", error);
        }
    }

    // 播放主菜单BGM的方法
    playHomeBGM() {
        console.log("尝试播放主菜单BGM");

        // 先停止任何正在播放的音乐
        AudioMgr.inst.stop();

        // 检查bgAudio是否已设置
        if (this.bgAudio) {
            // 延迟一帧再播放，确保音频系统已准备好
            this.scheduleOnce(() => {
                console.log("播放主菜单BGM");
                AudioMgr.inst.play(this.bgAudio, 0.1);
            }, 0);
        } else {
            console.error("无法播放主菜单BGM：bgAudio未设置");
        }
    }

    // 点击普通模式按钮
    onNormalModeBtnClick() {
        // 隐藏主面板，显示难度选择面板
        this.mainPanel.active = false;
        this.difficultyPanel.active = true;
    }

    // 点击挑战模式按钮
    onChallengeModeBtnClick() {
        // 隐藏主面板，显示挑战选择面板
        this.mainPanel.active = false;
        this.challengePanel.active = true;
    }

    // 点击金币商店按钮
    onShopButtonClick() {
        console.log("HomeUI: 进入金币商店");

        // 停止主菜单音乐
        AudioMgr.inst.stop();

        // 加载商店场景
        director.loadScene('Shop');
    }

    // 我们不需要在onDestroy中停止音乐
    // 因为当从游戏场景返回主菜单时，这会导致主菜单的BGM无法播放
    // 音乐的停止应该在切换到其他场景前进行，而不是在当前场景销毁时
}
